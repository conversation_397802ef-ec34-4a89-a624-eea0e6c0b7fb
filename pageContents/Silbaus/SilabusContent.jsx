import React, { useState, useEffect, useRef } from "react";
import { MdClose } from "react-icons/md";
import { useDispatch } from "react-redux";
import { setFormValue } from "../../redux/reducers/silabus/silabus";
import dynamic from "next/dynamic";
import apiClientProtected from "../../helpers/apiClient";
import axios from "axios";
import BaseFilterSelect from "../../components/base/BaseFilterSelect";
import { useRouter } from "next/router";

import ModalWrapper from "../../components/modal/modal";
//components
import CustomSelect from "../../components/select/CustomSelect";
// packages
import DropdownMultiselect from "react-multiselect-dropdown-bootstrap";

import { selectsArray, textareaArray, inputsArray } from "./formsArray";
import {
  weekDays,
  hoursRange,
  academicHonesty,
  preRequsitesData,
} from "./silabusData";
// styles
import {
  StyledContainer,
  StyledFormGroup,
  StyledFormTable,
  StyledTitle,
} from "./styles";
import "react-datepicker/dist/react-datepicker.css";
import Cookies from "js-cookie";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "./../../components/context/TableContext";

const importJodit = () => import("jodit-react");
const JoditEditor = dynamic(importJodit, {
  ssr: false,
});

const selectStyles = {
  backgroundColor: "#fff",
  border: "1px solid #e4e6ef",
};

const SilabusContent = () => {
  const editor = useRef(null);
  const [content, setContent] = useState("");
  const [lecturersOptions, setLecturersOptions] = useState([]);
  const [lecturerIds, setLecturersIds] = useState([]);
  const [lecturersData, setLecturersData] = useState([]);
  const [semesters, setSemesters] = useState([]);
  const [showConditions, setShowConditions] = useState(false);
  const [showPr, setShowPr] = useState(false);
  const [prerequisites, setPrerequisites] = useState([]);
  const [teachingMethods, setTeachingMethods] = useState([]);
  const [percentError, setPercentError] = useState({});
  const [examPercent, setExamPercent] = useState({});
  const [preSearch, setPreSearch] = useState("");
  const [sylabusStatus, setSylabusStatus] = useState([]);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});

  const { errors, setErrors, programId } = useTableContext();
  const router = useRouter();

  const [sylabus, setSylabus] = useState({
    title: "",
    academic_degree_id: "",
    status_id: "",
    semester_id: "",
    code: "",
    credits: "",
    contact_hours: 0,
    lecture_hours: 0,
    seminar_hours: 0,
    independent_work_hours: 0,
    mid_and_final_exam_hours: 0,
    total_hours: "",
    methods: [],
    literates: [
      {
        id: 1,
        title: "",
        number: 1,
        main_literature: "",
        secondary_literature: null,
      },
    ],
    academic_honesty: "",
    retake_missed_assignment: "",
    main_literature: "",
    additional_literature: "",
    additional_information: "",
    assessing_system: "",
    exams: [],
    prerequisites: [],
    lecturers: [],
    learning_outcome: {
      learning: "",
      skill: "",
      responsibility: "",
    },
  });

  useEffect(() => {
    const getLecturers = async () => {
      const response = await apiClientProtected().get(
        process.env.NEXT_PUBLIC_LECTURERS
      );
      console.log(response.data.lecturers.data);
      const result = response.data.lecturers.data.map((item) => {
        item.label = item.first_name + " " + item.last_name;
        return item;
      });

      setLecturersOptions(result);

      const programs = await apiClientProtected().get(`/programs`);
      console.log(programs.data.programs.data, "PRogromebi");
      const academicDegree = programs.data.programs.data.find(
        (item) => item.id === Number(programId)
      ).academic_degree;
      console.log(academicDegree);
      setSylabus({
        ...sylabus,
        academic_degree_id: academicDegree,
      });
    };

    getLecturers();

    const getMethods = async () => {
      const response = await apiClientProtected().get("/syllabi/create");
      console.log(response, "Axali konsoli");
      setTeachingMethods(response.data.methods);
      const semestersArray = [];
      for (let key in response.data.semesters) {
        semestersArray.push({ id: key, value: response.data.semesters[key] });
      }
      setSemesters(semestersArray);
      const pr = Object.entries(response.data.prerequisites).map((item) => {
        return { id: item[0], title: item[1] };
      });
      setPrerequisites(pr);

      const status = Object.entries(response.data.statuses).map(
        (item, index) => {
          return { id: item[0], title: item[1], code: index };
        }
      );

      setSylabusStatus(status);
    };
    console.log(router.query.flowId, "Flow id");
    getMethods();
  }, []);

  useEffect(() => {
    const l =
      sylabus.lecturers &&
      lecturersOptions.filter((item) => lecturerIds.includes(item.id));
    setLecturersData(l);
    const code =
      sylabus.academic_degree_id.code +
      "." +
      sylabus.semester_id +
      "01" +
      "." +
      sylabus.status_id;
    setSylabus({ ...sylabus, code });
  }, [lecturerIds, sylabus.semester_id, sylabus.status_id]);

  useEffect(() => {
    const total =
      Number(sylabus["contact_hours"]) +
      Number(sylabus["seminar_hours"]) +
      Number(sylabus["lecture_hours"]) +
      Number(sylabus["independent_work_hours"]) +
      Number(sylabus["mid_and_final_exam_hours"]);
    setSylabus({ ...sylabus, total_hours: total, credits: total / 25 });
  }, [
    sylabus.contact_hours,
    sylabus.seminar_hours,
    sylabus.lecture_hours,
    sylabus.independent_work_hours,
    sylabus.mid_and_final_exam_hours,
  ]);

  const handleChange = (e) => {
    console.log(e, sylabus);
    setSylabus({ ...sylabus, [e.target.name]: e.target.value });
  };

  const handleJoditChangeForm = (name, value) => {
    console.log(sylabus);
    setSylabus({ ...sylabus, [name]: value });
  };

  const generateCode = () => {};

  const handleExamPercent = (e) => {
    console.log(e.target.value);
    const value = Number(e.target.value);
    if (value >= 20 && value <= 50) {
      setPercentError({});
      setExamPercent(value);
    } else {
      setPercentError({
        ...percentError,
        [e.target.name]:
          "შეყვანილი რიცხვი უნდა იყოს არა უმცირეს 20-სა და არ უნდა აღემატებოდეს 50-ს",
      });
    }
  };

  const searchPrerequisites = () => {
    // setPreSearch(e.target.value)
    const filteredArray = preSearch
      ? prerequisites.filter(
          (item) =>
            item.title.toLowerCase().indexOf(preSearch.toLowerCase()) !== -1
        )
      : prerequisites;
    return filteredArray;
  };

  const handlePrDelete = (id) => {
    setSylabus({
      ...sylabus,
      prerequisites: sylabus.prerequisites.filter((item) => item.id !== id),
    });
    console.log(id);
  };

  const addPrerequisites = (value) => {
    if (!sylabus.prerequisites.includes(value)) {
      setSylabus({
        ...sylabus,
        prerequisites: [...sylabus.prerequisites, value],
      });
    }

    setPreSearch("");
  };

  const handleLecturers = (e, id) => {
    console.log(e.target.value, e.target.name, id);
    const newArray = sylabus.lecturers.map((item) => {
      if (id == item.id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setSylabus({
      ...sylabus,
      lecturers: newArray,
    });
  };

  const addMethod = (e) => {
    console.log(e.target.checked);
    const checkedMethod = JSON.parse(e.target.value);
    if (e.target.checked) {
      console.log(e.target.value);
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setTeachingMethods(checkedData);
      setSylabus({
        ...sylabus,
        methods: [...sylabus.methods, checkedMethod],
      });
    } else {
      const filteredMethods = sylabus.methods.filter(
        (item) => item.id !== checkedMethod.id
      );
      setSylabus({
        ...sylabus,
        methods: filteredMethods,
      });
      const checkedData = teachingMethods.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setTeachingMethods(checkedData);
    }
  };

  const handleExamDelete = (id) => {
    const examsData = sylabus.exams.filter((item) => item.id !== id);
    setSylabus({ ...sylabus, exams: examsData });
  };

  const handleExamData = (e, id) => {
    console.log(e.target.value, id);
    const exams = sylabus.exams.map((item) => {
      if (item.id === id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setSylabus({ ...sylabus, exams });
  };

  const handleRate = (data) => {
    console.log(data);
    const randomId = Math.random().toString().slice(2);
    console.log(randomId);
    const mergeId = { ...data, id: randomId };
    setSylabus({ ...sylabus, exams: [...sylabus.exams, mergeId] });
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    const mappedArray = arrData.map((item) => {
      return { id: item, week_day: "", start_time: "", end_time: "" };
    });

    console.log(mappedArray);
    const lecturer = {
      id: arrData[0],
      week_day: "",
      start_time: "",
      end_time: "",
    };
    setSylabus({ ...sylabus, [name]: mappedArray });
    setLecturersIds(arrData);
  };

  const handleAddWeeks = (e) => {
    e.preventDefault();
    const values = [...sylabus.literates];
    values.push({
      id: Math.floor(Math.random() * 100),
      number: sylabus.literates.length + 1,
      title: "",
      main_literature: null,
      additional_literature: null,
    });
    setSylabus({
      ...sylabus,
      literates: values,
    });
  };

  const handleRemoveWeek = (id) => {
    console.log(id);
    setSylabus({
      ...sylabus,
      literates: sylabus.literates.filter((input) => input.id !== id),
    });
  };

  const handleInputChange = (index, event) => {
    const values = [...sylabus.literates];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setSylabus({
      ...sylabus,
      literates: values,
    });
    console.log(sylabus);
  };

  const handleModalShow = () => {
    return true;
  };
  const handleModalClose = () => {
    return false;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log(sylabus);
    const fd = new FormData();

    fd.append("name", sylabus.title);
    fd.append("academic_degree_id", sylabus.academic_degree_id.id);
    fd.append("status_id", sylabus.status_id);
    fd.append("semester_id", Number(sylabus.semester_id));
    fd.append("code", "asdkjhadsq");
    fd.append("credits", sylabus.credits);
    fd.append("contact_hours", sylabus.contact_hours);
    fd.append("lecture_hours", sylabus.lecture_hours);
    fd.append("seminar_hours", sylabus.seminar_hours);
    fd.append("mid_and_final_exam_hours", sylabus.mid_and_final_exam_hours);
    fd.append("independent_work_hours", sylabus.independent_work_hours);
    fd.append("total_hours", sylabus.total_hours);
    fd.append("goal", sylabus.goal);
    fd.append("main_literature", sylabus.main_literature);
    fd.append("additional_literature", sylabus.additional_literature);
    fd.append("academic_honesty", academicHonesty);
    fd.append(
      "final_exam_prerequisite",
      `<p>დასკვნით  გამოცდაზე  დასაშვებად  სტუდენტს  გადალახული  უნდა ჰქონდეს  შუალედური  ჯამური  შეფასებების ${examPercent}</p><p>სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე სემესტრში. დამატებითი გამოცდის  შემთხვევაში, უნივერსიტეტი ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.</p>`
    );
    fd.append("exam_rules", "alkadlkjlaskdjljasd");
    fd.append("retake_missed_assignment", "qwoqw asdkjhaskdqwu oi");
    fd.append("assessing_system", "aslkjasduuyakjhsd");
    fd.append("assessing_componenets", "aslkjasduuyakjhsd");

    for (let i = 0; i < sylabus.literates.length; i++) {
      const arr = [];
      for (let key in sylabus.literates[i]) {
        console.log(sylabus.literates[i][key]);
        arr.push(sylabus.literates[i][key]);
        fd.append(`weeks[${i}][${key}]`, sylabus.literates[i][key]);
      }
    }

    for (let i = 0; i < sylabus.lecturers.length; i++) {
      const arr = [];
      for (let key in sylabus.lecturers[i]) {
        console.log(sylabus.lecturers[i][key]);
        arr.push(sylabus.lecturers[i][key]);
        fd.append(`lecturers[${i}][${key}]`, sylabus.lecturers[i][key]);
      }
    }

    for (let i = 0; i < sylabus.exams.length; i++) {
      const arr = [];
      for (let key in sylabus.exams[i]) {
        console.log(sylabus.exams[i][key]);
        arr.push(sylabus.exams[i][key]);
        fd.append(`exams[${i}][${key}]`, sylabus.exams[i][key]);
      }
    }

    for (let i = 0; i < sylabus.methods.length; i++) {
      fd.append(`method_ids[${i}]`, sylabus.methods[i].id);
    }

    try {
      const response = await apiClientProtected().post("/syllabi", fd);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: "წარმატება",
        text: "თქვენი სილაბუსი წარმატებით დაემატა",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setErrors(null);
      router.push("/admin/curriculum");
      console.log(response);
    } catch (err) {
      setSuccess(true);
      setErrors(err.response.data);
      console.log(err.response, "Gubazi");
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>სილაბუსის შექმნა {generateCode()}</StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის დასახელება</span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="title"
              value={sylabus.title}
              placeholder="კურსის დასახელება"
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის საფეხური</span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="academic_degree_id"
              placeholder="კურსის დასახელება"
              value={sylabus.academic_degree_id.name_ka}
              onChange={handleChange}
              disabled
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის სტატუსი</span>
          </div>
          <div className="right__side">
            {/* <input
              className="form-control"
              value={sylabus.status_id.title}
              name="status_id"
              placeholder="კურსის დასახელება"
              onChange={handleChange} /> */}
            <select
              name="status_id"
              className="form-select"
              value={sylabus.status_id}
              onChange={handleChange}
            >
              {sylabusStatus.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.title}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.status_id}</div>}
          </div>
        </StyledFormGroup>

        {selectsArray.map((item) => (
          <StyledFormGroup key={item.id}>
            {" "}
            <div className="left__side">
              <span className="text-bold">{item.label}</span>
            </div>
            <div className="right__side">
              <CustomSelect
                name={item.name}
                value={sylabus[item.name]}
                options={semesters}
                onChange={handleChange}
              />
            </div>
          </StyledFormGroup>
        ))}

        {inputsArray.map((item) =>
          item.children ? (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">{item.label}</span>
              </div>
              <div className="right__side">
                {item.children.map((input) => (
                  <div className="input__groups" key={input.id}>
                    <span>{input.label}</span>
                    <input
                      type={input.type}
                      className="form-control"
                      placeholder="საათი"
                      name={input.name}
                      value={sylabus[input.name]}
                      onChange={handleChange}
                    />
                    {errors && (
                      <div className="text-danger">{errors[input.name]}</div>
                    )}
                  </div>
                ))}
              </div>
            </StyledFormGroup>
          ) : (
            <StyledFormGroup key={item.id}>
              <div className="left__side">
                <span className="text-bold">{item.label}</span>
              </div>
              <div className="right__side">
                <input
                  className="form-control"
                  placeholder={item.placeholder}
                  name={item.name}
                  value={sylabus[item.name]}
                  disabled={item.disabled}
                  onChange={handleChange}
                />
                {errors && (
                  <div className="text-danger">{errors[item.name]}</div>
                )}
              </div>
            </StyledFormGroup>
          )
        )}
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              კურსის განმახროციელებელი/ ლექტორი საკონტაქტო ინფორმაცია
              სტუდენტებთან საკონსულტაციო შეხვედრისთვის გამოყოფილი დღე და საათი
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <BaseFilterSelect
                data={lecturersOptions}
                name="lecturers"
                setValue={handleFilterValue}
                searchable={true}
                multiSelect={true}
                selectStyles={selectStyles}
                placeholder="ლექტორის არჩევა"
              />
              {errors && <div className="text-danger">{errors.lecturers}</div>}
            </div>
            <ul>
              {lecturersData &&
                lecturersData.map((item) => (
                  <li key={item.id} className="my-4">
                    <h5 className="mb-2">
                      {item.first_name + " " + item.last_name}
                    </h5>
                    <div className="mb-2">{item.email}</div>
                    <div className="mb-2">{item.phone}</div>
                    <div className="d-flex gap-4">
                      <select
                        className="form-select"
                        name="week_day"
                        onChange={(e) => handleLecturers(e, item.id)}
                      >
                        {weekDays.map((item, index) => (
                          <option key={index} value={item.id}>
                            {item.name}
                          </option>
                        ))}
                      </select>
                      <select
                        className="form-select"
                        name="start_time"
                        onChange={(e) => handleLecturers(e, item.id)}
                      >
                        {hoursRange.map((item, index) => (
                          <option key={index} value={item}>
                            {item}
                          </option>
                        ))}
                      </select>
                      <select
                        className="form-select"
                        name="end_time"
                        onChange={(e) => handleLecturers(e, item.id)}
                      >
                        {hoursRange.map((item, index) => (
                          <option key={index} value={item}>
                            {item}
                          </option>
                        ))}
                      </select>
                    </div>
                  </li>
                ))}
            </ul>
            {/* <div className="select__groups">
              <CustomSelect options={weekDays} onChange={handleChange} />
              <CustomSelect
                options={["დაწყების საათი"]}
                onChange={handleChange}
              />
              <CustomSelect
                options={["დასრულების საათი"]}
                onChange={handleChange}
              />
            </div> */}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სასწავლო კურსის მიზნები</span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={content}
              name="goal"
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({ ...sylabus, goal: newContent });
              }}
            />
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">საგანზე დაშვების წინაპირობა/ები</span>
          </div>
          <div className="right__side">
            <div className="mb-4">
              <input
                type="checkbox"
                className="form-check-input mx-1"
                id="condition"
                onChange={() => setShowConditions(!showConditions)}
                value={showConditions}
                name="conditions"
              />
              <label htmlFor="condition" className="mx-2">
                წინაპირობა
              </label>
            </div>
            {showConditions && (
              <div className="">
                <div className="position-relative">
                  <input
                    type="text"
                    className="form-control"
                    name="preSearch"
                    value={preSearch}
                    onChange={(e) => setPreSearch(e.target.value)}
                    placeholder="წინაპირობის ძებნა"
                  />
                  <div className="mt-2 d-flex">
                    {sylabus.prerequisites.map((item, index) => (
                      <span className="pre-badge" key={index}>
                        <span>{item.title}</span>
                        <MdClose onClick={() => handlePrDelete(item.id)} />
                      </span>
                    ))}
                  </div>
                  {
                    <ul
                      className={`pre-dropdown ${
                        preSearch.length > 0 && "d-block"
                      }`}
                    >
                      {searchPrerequisites().map((item, index) => (
                        <li key={index} onClick={() => addPrerequisites(item)}>
                          {item.title}
                        </li>
                      ))}
                    </ul>
                  }
                </div>
              </div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სწავლება-სწავლის მეთოდები</span>
          </div>
          <div className="right__side">
            {/* <div className="input__groups">
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  value={false}
                  onChange={(e) => addAllMethods(e)}
                  id="discus" />
                  <label className="form-check-label" htmlFor="discus">
                    <div className="mb-2 text-bold">ყველას არჩევა</div>
                  </label>
              </div>
            </div> */}
            {teachingMethods &&
              teachingMethods.map((item) => (
                <div className="input__groups" key={item.id}>
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      value={JSON.stringify(item)}
                      checked={item.isAdded}
                      onChange={(e) => addMethod(e)}
                    />
                    <label className="form-check-label" htmlFor="discus">
                      <div className="mb-2 text-bold">{item.title}</div>
                      {item.text}
                    </label>
                  </div>
                </div>
              ))}
            {errors && <div className="text-danger">{errors.method_ids}</div>}
            {/* <div className="input__groups">
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  value=""
                  id="groupWork"
                />
                <label className="form-check-label" htmlFor="groupWork">
                  ჯგუფური მუშაობა
                </label>
              </div>
            </div> */}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <StyledFormTable>
            <div>
              <div className="row">
                <div className="col col-lg-1 item">კვირა</div>
                <div className="col-5 item">
                  სასწავლო კურსის/მოდულის შინაარსი (თემები და აქტივობები)
                </div>
                <div className="col-sm item">ძირითადი ლიტერატურა</div>
                <div className="col-sm item">დამატებითი ლიტერატურა</div>
                <div className="col-sm item" style={{ borderRight: "none" }}>
                  დამატებითი ლიტერატურა{" "}
                </div>
              </div>
            </div>
            {sylabus.literates.map((item, index) => (
              <div key={item.id}>
                <div className="row">
                  <div className="col col-lg-1 item">{index + 1}</div>
                  <div className="col-5 item">
                    <input
                      className="form-control"
                      name="title"
                      onChange={() => handleInputChange(index, event)}
                    />
                  </div>
                  <div className="col-sm item">
                    <input
                      className="form-control"
                      name="main_literature"
                      onChange={() => handleInputChange(index, event)}
                    />
                  </div>
                  <div className="col-sm item">
                    <input
                      className="form-control"
                      name="secondary_literature"
                      onChange={() => handleInputChange(index, event)}
                    />
                  </div>
                  <div className="col-sm item" style={{ borderRight: "none" }}>
                    {sylabus.literates.length !== 1 && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          return handleRemoveWeek(item.id);
                        }}
                        className="btn btn-danger"
                      >
                        -
                      </button>
                    )}{" "}
                    {sylabus.literates.length - 1 === index && (
                      <button
                        className="btn btn-primary"
                        onClick={handleAddWeeks}
                      >
                        +
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </StyledFormTable>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">შეფასების სისტემა</span>
          </div>
          <div className="right__side">
            <p className="mb-4 text-bold">
              სწავლის/სწავლების მეთოდები და სტუდნტის შეფასების სისტემა
              შეესაბამება საქართველოს განათლებისა და მეცნიერების მინისტრის №3
              ბრძანებას.
            </p>
            <p>შეფასების სისტემა უშვებს:</p>
            <p>ხუთი სახის დადებით შეფასებას:</p>
            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>ა) (A) ფრიადი – შეფასების 91-100 ქულა;</li>
              <li>ბ) (B) ძალიან კარგი – შეფასების 81-90 ქულა;</li>
              <li>გ) (C) კარგი – შეფასების 71-80 ქულა;</li>
              <li>დ) (D) დამაკმაყოფილებელი – შეფასების 61-70 ქულა;</li>
              <li>ე) (E) საკმარისი – შეფასების 51-60 ქულა.</li>
            </ul>

            <p className="text-bold">ორი სახის უარყოფით შეფასებას:</p>

            <ul className="my-4" style={{ paddingLeft: "2rem" }}>
              <li>
                ა) (Fx) ვერ ჩააბარა – მაქსიმალური შეფასების 41-50 ქულა, რაც
                ნიშნავს, რომ სტუდენტს ჩასაბარებლად მეტი მუშაობა სჭირდება და
                ეძლევა დამოუკიდებელი მუშაობით დამატებით გამოცდაზე ერთხელ გასვლის
                უფლება;
              </li>
              <li>
                ბ) (F) ჩაიჭრა – მაქსიმალური შეფასების 40 ქულა და ნაკლები, რაც
                ნიშნავს, რომ სტუდენტის მიერ ჩატარებული სამუშაო არ არის საკმარისი
                და მას საგანი ახლიდან აქვს შესასწავლი.
              </li>
            </ul>

            <p className="text-bold">
              სტუდენტს კრედიტი ენიჭება კანონმდებლობით გათვალისწინებული ერთ-ერთი
              დადებითი შეფასების მიღების შემთხვევაში.
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              დასკვნით გამოცდაზე დაშვების წინაპირობა და დამატებით გამოცდაზე
              გასვლის პირობა
            </span>
          </div>
          <div className="right__side">
            {/* <JoditEditor
              ref={editor}
              value={content}
              tabIndex={1} // tabIndex of textarea
              onBlur={(newContent) => {
                return setContent(newContent);
              }}
              onChange={(newContent) => {
                console.log(newContent);
              }}
            /> */}

            <div>
              დასკვნით გამოცდაზე დასაშვებად სტუდენტს გადალახული უნდა ჰქონდეს
              შუალედური ჯამური შეფასებების
              <input
                type="text"
                name="examPercent"
                className="form-sm-control"
                onChange={handleExamPercent}
                placeholder="ჩაწერეთ პროცენტი"
              />{" "}
              %
              {percentError && percentError.examPercent ? (
                <div className="text-danger">{percentError.examPercent}</div>
              ) : null}
              <p className="mt-4">
                სტუდენტს დამატებით გამოცდაზე გასვლის უფლება აქვს იმავე
                სემესტრში. დამატებითი გამოცდის შემთხვევაში, უნივერსიტეტი
                ვალდებულია დამატებითი გამოცდა დანიშნოს დასკვნითი გამოცდის
                შედეგების გამოცხადებიდან არანაკლებ 5 დღეში. დამატებით გამოცდაზე
                გასვლის შემთხვევაში, ფინალური გამოცდის ქულა განულდება და მის
                ნაცვლად დაფიქსირდება დამატებით გამოცდაზე მიღებული შეფასება.
              </p>
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div
            className="left__side"
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <span className="text-bold">შეფასებები</span>
            <ModalWrapper
              handleModalShow={handleModalShow}
              handleModalClose={handleModalClose}
              handleRate={handleRate}
              title="შეფასების დამატება"
            ></ModalWrapper>
          </div>
          <div className="right__side">
            {sylabus.exams.length > 0 && (
              <div>
                <table className="w-100 border bg-white">
                  <thead>
                    <tr>
                      <th className="border p-2" style={{ width: "10%" }}>
                        სათაური
                      </th>
                      <th className="border p-2" style={{ width: "50%" }}>
                        გამოცდის აღწერა
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        შეფასება
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        მინ. ზღვარი
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        რაოდენობა
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        მოქმედება
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {sylabus.exams.map((item) => (
                      <tr key={item.id}>
                        <td className="border p-2">{item.title}</td>
                        <td className="border p-2">
                          <textarea
                            name="description"
                            className="form-control"
                            onChange={(e) => handleExamData(e, item.id)}
                          />
                        </td>
                        <td className="border p-2">{item.score}</td>
                        <td className="border p-2">{item.minRate}</td>
                        <td className="border p-2">{item.number_of_childs}</td>
                        <td className="border p-2">
                          <button
                            className="btn btn-danger"
                            onClick={() => handleExamDelete(item.id)}
                          >
                            -
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            {errors && <div className="text-danger">{errors.exams}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              გაცდენილი შეფასებითი კომპონენტის აღდგენა
            </span>
          </div>
          <div className="right__side">
            {/* <JoditEditor
              ref={editor}
              value={content}
              tabIndex={1} // tabIndex of textarea
              onBlur={(newContent) => {
                return setContent(newContent);
              }}
              onChange={(newContent) => {
                console.log(newContent);
              }}
            /> */}
            <p>
              არასაპატიო მიზეზით გაცდენილი შეფასებით გათვალისწინებული აქტივობები
              აღდგენას არ ექვემდებარება.
            </p>
          </div>
        </StyledFormGroup>

        {sylabus.exams.length > 0 &&
          textareaArray.map((item) => {
            return (
              sylabus.exams.find((x) => x.title === item.label) && (
                <StyledFormGroup key={item.id}>
                  <div className="left__side">
                    <span>{item.label}</span>
                  </div>
                  <div className="right__side">
                    <JoditEditor
                      ref={editor}
                      value={content}
                      tabIndex={1} // tabIndex of textarea
                      onBlur={(newContent) => {
                        return setContent(newContent);
                      }}
                      onChange={(value) => {
                        handleJoditChangeForm("", value);
                      }}
                    />
                  </div>
                </StyledFormGroup>
              )
            );
          })}

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">ძირითადი ლიტერატურა</span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={content}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({ ...sylabus, main_literature: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.main_literature}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">დამხმარე ლიტერატურა</span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              value={content}
              tabIndex={1} // tabIndex of textarea
              onChange={(newContent) => {
                console.log(newContent);
                setSylabus({ ...sylabus, additional_literature: newContent });
              }}
            />
            {errors && (
              <div className="text-danger">{errors.additional_literature}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="right__side">
            <span>
              <p className="text-bold">
                ლექციის, სემინარის ან გამოცდის მსვლელობისას (თუ ეს წინასწარ არ
                არის დაშვებული ლექტორის მიერ) იკრძალება:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>დაგვიანება;</li>
                <li>
                  ლექციის მსვლელობისას ლექციის უნებართვოდ დატოვება და დატოვების
                  შემთხვევაში უკან დაბრუნება;
                </li>
                <li>ხმაური;</li>
                <li>ტელეფონის ან სხვა მოწყობილობის გამოყენება;</li>
                <li>
                  და სხვა ქმედება, რომელიც ხელს შეუშლის სასწავლო პროცესის
                  მიმდინარეობას;
                </li>
              </ul>
              <p className="text-bold">
                აკრძალული ქცევების სასწავლო პროცესში აღმოჩენის შემთხვევაში
                სტუდენტის მიმართ შეიძლება გავრცელდეს შემდეგი სანქციები:
              </p>
              <ul
                className="my-4"
                style={{ paddingLeft: "2rem", listStyle: "circle" }}
              >
                <li>შენიშვნა;</li>
                <li> საყვედური;</li>
                <li>სხვა პასუხისმგებლობა;</li>
              </ul>
            </span>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              აკადემიური კეთილსინიდსიერების დაღვევა - პლაგიატი
            </span>
          </div>
          <div className="right__side">
            {/* <JoditEditor
              ref={editor}
              tabIndex={1} // tabIndex of textarea
              onChange={(value) =>
                handleJoditChangeForm("academic_honesty", value)
              }
            /> */}
            <p className="mb-4">
              პლაგიატად მიიჩნევა სხვა ავტორის ნაშრომის, იდეის, მოსაზრების,
              გამონათქვამის უკანონო მითვისება, იმიტირება, ციტირების არსებული
              მოთხოვნების დარღვევით და/ან წყაროს მითითების გარეშე.
            </p>
            <p className="mb-4">
              აკადემიური კეთილსინდისიერების დარღვევად ითვლება სხვა სტუდენტისაგან
              ან წინასწარ მომზადებული კონსპექტიდან ან სხვა წყაროდან გადაწერა, რა
              შემთხვევაშიც გამოცდის ან დავალების აღდგენა არ ხდება და სტუდენტს ამ
              შეფასების შესაბამის კომპონენტში დაეწერება 0 ქულა.
            </p>
            <p className="mb-4">
              პლაგიატის შემთხვევაში (მათ შორის უნებლიე), სტუდენტს მოცემულ
              საგანში ავტომატურად უფორმდება არადამაკმაყოფილებელი შეფასება (F).
            </p>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">სწავლის შედეგები</span>
          </div>
          <div className="right__side">
            <div className="input__groups">
              <span>ცოდნა და გაცნობიერება:</span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({
                    ...sylabus,
                    learning_outcome: {
                      ...sylabus.learning_outcome,
                      learning: newContent,
                    },
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span>უნარი:</span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({
                    ...sylabus,
                    learning_outcome: {
                      ...sylabus.learning_outcome,
                      skill: newContent,
                    },
                  });
                }}
              />
            </div>

            <div className="input__groups">
              <span className="text-bold">
                პასუხისმგებლობა და ავტონომიურობა:
              </span>
              <JoditEditor
                ref={editor}
                value={content}
                tabIndex={1} // tabIndex of textarea
                onChange={(newContent) => {
                  console.log(newContent);
                  setSylabus({
                    ...sylabus,
                    learning_outcome: {
                      ...sylabus.learning_outcome,
                      responsibility: newContent,
                    },
                  });
                }}
              />
            </div>
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">დამატებითი ინფორმაცია/პირობები</span>
          </div>
          <div className="right__side">
            <JoditEditor
              ref={editor}
              tabIndex={1} // tabIndex of textarea
              onChange={(value) =>
                handleJoditChangeForm("additional_information", value)
              }
            />
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side"></div>
          <div className="right__side">
            <button className="btn btn-primary" type="submit">
              შექმნა
            </button>
          </div>
        </StyledFormGroup>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </StyledContainer>
  );
};

export default SilabusContent;
