import { useState, useEffect } from "react";
import styled from "styled-components";
import Link from "next/link";
import {
  lectureEdit,
  bookBlack,
  clockBlack,
  star,
  syllabus,
} from "../lecturer/lecturerSvg";
import {
  MdOutlineChevronRight,
  MdExpandMore,
  MdSettings,
  MdCheck,
  MdClose,
  MdOutlineRemove,
} from "react-icons/md";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { MdKeyboardArrowDown } from "react-icons/md";
import BookModal from "./../student/library/BookModal";
import apiClientProtected from "../../helpers/apiClient";

const CurrentProfessionItem = ({ subject, openDetailsHandler, id }) => {
  const { locale } = useLocaleContext();
  const [headings, setHeadings] = useState([]);
  const [data, setData] = useState([]);
  const [dataColumns, setDataColumns] = useState([]);
  const [open, setOpen] = useState(false);
  const [missedLectures, setMissedLectures] = useState([]);

  useEffect(() => {
    const getTableData = async () => {
      const responseHeadings =
        subject.headings && Object.values(subject.headings);

      setHeadings(responseHeadings);
    };
    getTableData();
  }, []);

  const handleMissedLectures = async (syllabusId) => {
    try {
      const response = await apiClientProtected().get(
        `/administration/student-current-missed-hours/${id}/${syllabusId}`
      );
      setMissedLectures(response.data.data);
      setOpen(true);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <>
      <Subject>
        <SubDescription>
          <div>
            <h4 onClick={() => openDetailsHandler(subject.id)}>
              {locale === "ka" ? subject.name : subject.name_en} -{" "}
              {subject.code} - ID: {subject.id}
            </h4>
            <div>
              <ul>
                {subject.lecturers?.map((item, index) => (
                  <li key={index}>
                    <p>
                      {item.first_name} {item.last_name}
                    </p>

                    {subject.lecturers.length > 1 && ", "}
                  </li>
                ))}
              </ul>
              <span></span>
              <ul>
                <li>
                  {star}
                  <p>
                    {" "}
                    {locale && langs[locale]["acts"]}: {subject.credits}
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </SubDescription>
        <Total isDetailsOpen={subject.all_score}>
          <div>
            <div>
              <h4>ჯამური ქულა</h4>
              <TotalCellCheck isActive={subject.total}>
                <div style={{ backgroundColor: subject.total ? '#0abe28' : '#c91616' }}>
                  {subject.total ? (
                      <MdCheck color="#fff" />
                  ) : (
                      <MdClose color="#fff" />
                  )}
                </div>
              </TotalCellCheck>
            </div>
          </div>
        </Total>
      </Subject>

      <Details isDetailsOpen={subject.all_score}>
        <SubjectTabe>
          <thead>
            <tr>
              {headings?.map((item, index) => (
                <th colSpan={item.colSpan} rowSpan={item.rowSpan} key={index}>
                  {locale === "ka" ? item.name_ka : item.name_en}{" "}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td onClick={() => handleMissedLectures(subject.id)}>
                {subject.missedLectures} - {subject.missedLecturesInPercent}%
              </td>
              {subject.assignments.map((assign, index) => (
                <>
                  <td>
                    <div className="d-flex justify-content-center gap-4">
                      {assign.lastCheckout.map((sign, index) => (
                        <TableCellCheck
                          key={index}
                          id={sign.id}
                          isActive={sign.date}
                        >
                          <div>
                            {sign.id === 1 ? (
                              <MdCheck color="#fff" />
                            ) : sign.id === 2 ? (
                              <MdOutlineRemove color="#fff" />
                            ) : sign.id === 3 ? (
                              <MdClose color="#fff" />
                            ) : (
                              <MdCheck color="#fff" />
                            )}
                          </div>
                          <span style={{ whiteSpace: "nowrap" }}>
                            {sign[`name_${locale}`]}
                          </span>
                        </TableCellCheck>
                      ))}
                    </div>
                  </td>
                  <td>
                    <TableCellCheck
                      key={index}
                      isActive={assign.lastCheckoutFinal[0].date}
                      id={assign.lastCheckoutFinal[0].id}
                    >
                      <div>
                        {assign.lastCheckoutFinal[0].id === 1 ? (
                          <MdCheck color="#fff" />
                        ) : assign.lastCheckoutFinal[0].id === 2 ? (
                          <MdOutlineRemove color="#fff" />
                        ) : assign.lastCheckoutFinal[0].id === 3 ? (
                          <MdClose color="#fff" />
                        ) : (
                          <MdCheck color="#fff" />
                        )}
                      </div>
                      დადასტურება
                    </TableCellCheck>
                  </td>
                </>
              ))}
            </tr>
          </tbody>
        </SubjectTabe>
      </Details>

      <BookModal
        setOpen={setOpen}
        open={open}
        title={locale && langs[locale]["title"]}
      >
        <MissedLectures>
          <table className="missed-table">
            <thead>
              <tr>
                <th>N</th>
                <th>თარიღი</th>
                <th>ლექცია</th>
              </tr>
            </thead>
            <tbody>
              {missedLectures?.map((item, index) => (
                <tr key={index}>
                  <td>{index + 1}</td>
                  <td>{item.lecture.lecture_date}</td>
                  <td>{item.nth_lecture}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </MissedLectures>
      </BookModal>
    </>
  );
};

const Subject = styled.div`
  width: 100%;
  display: flex;
  border-top: 1px solid #e3e3e3;
  border-left: 1px solid #e3e3e3;
  border-right: 1px solid #e3e3e3;
  justify-content: space-between;
  background: linear-gradient(180deg, #f6f9ff 0%, #f6f9ff 100%);
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  align-items: center;
  padding: 6px 20px;
  overflow: hidden;

  margin-bottom: 2px;
  @media (max-width: 1245px) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const Details = styled.div`
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1rem;
  transition: all 300ms;
  :last-child {
    border-radius: 0 0 14px 14px;
    padding-bottom: ${(props) => (props.isDetailsOpen ? "0" : "0")};
  }
`;

const SubDescription = styled.div`
  display: flex;
  align-items: center;
  img {
    width: 46px;
    height: 46px;
    border-radius: 50%;
  }
  div {
    margin-left: 15px;
    h4 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      display: flex;
      align-items: center;
      letter-spacing: -0.03em;
      color: #953849;
      margin-bottom: 5px;
      @media (max-width: 560px) {
        font-size: 14px;
      }
    }
    a {
      color: #953849;
      font-weight: 700;
    }
    div {
      margin: 0;
      display: flex;
      align-items: center;
      p {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        display: flex;
        align-items: center;
        letter-spacing: -0.25px;
        color: #000000;
        transition: all 0.5s ease;
        /* :hover {
          font-weight: 600;
        } */
        @media (max-width: 560px) {
          font-size: 12px;
        }
      }
      display: flex;
      ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        span {
          margin: 0 12px 0 0;
          @media (max-width: 781px) {
            display: none;
          }
        }
        li {
          display: flex;
          align-items: center;
          margin-right: 12px;
          @media (max-width: 781px) {
            :nth-child(n + 4),
            :nth-child(2n + 3) {
              display: none;
            }
          }
          svg {
            path {
            }
            margin-right: 8px;
            @media (max-width: 560px) {
              width: 10px;
            }
          }
        }
      }
      span {
        height: 20px;
        width: 1px;
        display: block;
        background-color: #abb4c5;
        margin: 0 12px;
      }
    }
  }
`;

const TableCellCheck = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
  align-items: center;
  @media (max-width: 1245px) {
    width: 100% !important;
    display: flex;
  }
  div {
    width: 25px;
    height: 25px;
    display: flex;
    opacity: 1;
    justify-content: center !important;
    align-items: center !important;
    background: ${({ isActive, id }) =>
      isActive && id === 1
        ? "#0abe28"
        : isActive && id === 2
        ? "#c91616"
        : isActive && id === 3
        ? "#d1bd09"
        : isActive && id === 4
        ? "#0a58be"
        : "#a0b3eb"};
    border-radius: 50%;
  }
`;

const TotalCellCheck = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
  align-items: center;
  @media (max-width: 1245px) {
    width: 25px !important;
    display: flex;
  }
  div {
    width: 25px;
    height: 25px;
    display: flex;
    opacity: 1;
    justify-content: center !important;
    align-items: center !important;
    background: ${({ isActive, id }) =>
      isActive && id === 1
        ? "#0abe28"
        : isActive && id === 2
        ? "#c91616"
        : isActive && id === 3
        ? "#d1bd09"
        : isActive && id === 4
        ? "#0a58be"
        : "#a0b3eb"};
    border-radius: 50%;
  }
`;

const Total = styled.div`
  max-width: 275px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 45px;
  h4 {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    display: flex;
    align-items: center;
    letter-spacing: -0.03em;
    color: #333333;
    padding: 12px 0;
  }
  p {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
  }
  @media (max-width: 1290px) {
    max-width: 230px;
  }
  @media (max-width: 1245px) {
    flex-direction: column;
    align-items: flex-start;
    max-width: 100%;
    padding: 0;
    div {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      :first-child {
        border-bottom: solid 1px #e2e3e9;
      }
    }
    h4 {
      display: block;
    }
  }
  button {
    border: none;
    outline: none;
    cursor: pointer;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 50%;
    border-radius: 50%;

    transition: all 0.3s ease;
    svg {
      transition: all 0.3s ease;
      transform: ${(props) =>
        props.isDetailsOpen ? "rotate(180deg)" : "rotate(0deg)"};
    }
  }
`;

const SubjectTabe = styled.table`
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #ccc;
  /* @media (max-width: 1245px) {
    display: flex;
    justify-content: space-between;
    background-color: #b3cbff;
  } */
  thead {
    @media (max-width: 1245px) {
      background-color: #e4e8f3;
    }
    background-color: #e4e8f3;
    tr {
      /* @media (max-width: 1245px) {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      } */
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 17px;
      text-align: left;
      color: #333333;
      th {
        padding: 12px 20px !important;
        text-align: center;
        border: 2px solid #ddd;
        /* @media (max-width: 1245px) {
          br {
            display: none;
            text-align: start;
          }
        } */
      }
    }
  }
  tbody {
    background-color: #fff;
    @media (max-width: 1245px) {
      background-color: #fff;
    }
    tr {
      /* @media (max-width: 1245px) {
        display: flex;
        flex-direction: column;
      } */
      text-align: start;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      td {
        text-align: center;
        border: 1px solid #eee;
        padding: 12px 20px !important;
        &:nth-child(1) {
          cursor: pointer;
        }
      }
    }
  }
`;

const MissedLectures = styled.div`
  .missed-table {
    margin-top: 28px;
    width: 100%;
    thead {
      background-color: #953849;
      tr {
        th {
          padding: 19px 10px;
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 700;
          font-size: 16px;
          line-height: 19px;
          letter-spacing: -0.03em;
          color: #ffffff;
        }
      }
    }
    tbody {
      td {
        padding: 10px;
        background-color: #dde5f8;
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 17px;
        letter-spacing: -0.03em;
        color: #333333;
        /* text-align: center; */
        :first-child {
          text-align: start;
        }
      }
    }
  }
`;

export default CurrentProfessionItem;
