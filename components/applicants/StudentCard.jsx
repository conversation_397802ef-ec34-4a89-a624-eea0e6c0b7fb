import React, { useState, useEffect } from "react";
import jsPD<PERSON> from "jspdf";
import html2canvas from "html2canvas";
import Link from "next/link";
import {
  Container,
  Headline,
  ImageContainer,
  HeadTitle,
  Content,
  Export,
  FilesList,
  Thumb,
  ThumbInner,
} from "./styles";
// import {
//   DEGREE,
//   TITLES,
//   APPOINTMENTS,
//   GENDER,
//   LECTURER_CATEGORY_ID,
//   WORK,
// } from "./hrData";
import { AiFillHome, AiFillPhone } from "react-icons/ai";
import {
  MdEmail,
  MdCheck,
  MdClose,
  MdOutlineFileCopy,
  MdOutlinePrint,
} from "react-icons/md";
import { FaWpforms, FaGraduationCap } from "react-icons/fa";
import { GiPerson } from "react-icons/gi";
import { GrDocumentPdf, GrDocumentWord, GrBook } from "react-icons/gr";
import { LANG_LEVELS } from "./../projectData";

import { langs } from "./../locale";
import { useLocaleContext } from "./../context/LocaleContext";
import apiClientProtected from "../../helpers/apiClient";
import { getBirthDate } from "../../helpers/funcs";

const GENDER = [
  { id: 1, name: "მამრობითი" },
  { id: 0, name: "მდედრობითი" },
];

const DEGREE = [
  { id: 1, name: "ბაკალავრი" },
  { id: 2, name: "მაგისტრი" },
  { id: 3, name: "დოქტორი" },
];

const StudentCard = React.memo(({ data, routeName }) => {
  const [user, setUser] = useState([]);
  const { locale } = useLocaleContext();
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [adminPositions, setAdminPositions] = useState([]);

  useEffect(() => {
    let age = "";
    if (data.date_of_birth) {
      const date = new Date(
        data.date_of_birth.split("-").reverse().join("-")
      ).getTime();
      const now = new Date().getTime();
      age = Math.floor((now - date) / (1000 * 60 * 60 * 24 * 365));
    }
    setUser({ ...data, age });
    const getData = async () => {
      const response = await apiClientProtected().get("/schools");
      const responsePrograms = await apiClientProtected().get("/programs");
      const positionResponse = await apiClientProtected().get(
        "/administration-positions"
      );
      setAdminPositions(positionResponse.data.data);
      console.log(positionResponse, "asdddddddd");
      setSchools(response.data.schools.data);
      setPrograms(responsePrograms.data.programs.data);
      console.log(response, responsePrograms, "my data");
    };
    getData();

    // console.log(WORK, data.work_type_id);
  }, []);

  const generateFile = (id) => {
    const content = document.getElementById(`content-${id}`);
    html2canvas(content, {
      logging: true,
      letterRendering: 1,
      useCORS: true,
    }).then((canvas) => {
      const imgWidth = 208;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      const imgData = canvas.toDataURL("img/png");
      const pdf = new jsPDF("p", "mm", "a4");
      pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
      pdf.save("file.pdf");
    });
  };

  return (
    <Container id={`content-${data.id}`}>
      <Headline>
        <ImageContainer>
          <div>
            <img
              src={
                user.photo && user.photo.includes(".")
                  ? process.env.NEXT_PUBLIC_STORAGE + user.photo
                  : "/icons/user.png"
              }
              alt=""
            />
          </div>
          <HeadTitle>
            <h1>
              {user.first_name} {user.last_name}
            </h1>
            <span>
              {locale && langs[locale][routeName]} -{" "}
              {user.program && user.program.name_ka}
            </span>
          </HeadTitle>
        </ImageContainer>
        <ul>
          <li>
            <AiFillHome /> {user.address ? user.address : "N/A"}
          </li>
          <li>
            <AiFillPhone /> +995 {user.phone ? user.phone : "N/A"}
          </li>
          <li>
            <MdEmail /> {user.email ? user.email : "N/A"}
          </li>
        </ul>
      </Headline>
      <Content>
        <div>
          <h2>
            <span>
              <FaWpforms />
            </span>
            {locale && langs[locale]["personal_information"]}
          </h2>
          <ul>
            <li>
              <b>ID</b> {user.identity_number ? user.identity_number : "N/A"}
            </li>
            <li>
              <b>{locale && langs[locale]["date_of_birth"]}</b>
              {user.date_of_birth ? getBirthDate(user.date_of_birth) : "N/A"}
            </li>
            <li>
              <b>{locale && langs[locale]["age"]}</b>{" "}
              {user.age ? user.age : "N/A"}
            </li>
            <li>
              <b>{locale && langs[locale]["gender"]}</b>{" "}
              {user.gender
                ? GENDER.find((item) => item.id === user.gender).name
                : user.gender === 0
                ? GENDER.find((item) => item.id === user.gender).name
                : "N/A"}
            </li>
          </ul>
        </div>

        <div>
          <h2>
            <span>
              <GiPerson />
            </span>
            {locale && langs[locale]["education"]}
          </h2>
          <ul>
            <li>
              <b>{locale && langs[locale]["english_level_id"]}</b>{" "}
              {user.english_level_id
                ? LANG_LEVELS.find((item) => item.id === user.english_level_id)
                    .name
                : "N/A"}
            </li>
          </ul>
        </div>

        <div>
          <h2>
            <span>
              <MdOutlineFileCopy />
            </span>
            {locale && langs[locale]["attached_files"]}
          </h2>
          <FilesList>
            <Thumb>
              <ThumbInner>
                {user.identity_number_copy ? (
                  <Link
                    href={`${process.env.NEXT_PUBLIC_STORAGE}${user.identity_number_copy}`}
                  >
                    <a target="_blank">
                      <GrDocumentPdf />
                      პირადობის ასლი
                    </a>
                  </Link>
                ) : (
                  <>
                    <GrDocumentPdf />
                  </>
                )}
              </ThumbInner>
            </Thumb>
            <Thumb>
              <ThumbInner>
                {user.school_document && (
                  <Link
                    href={`${process.env.NEXT_PUBLIC_STORAGE}${user.school_document}`}
                  >
                    <a target="_blank">
                      <GrDocumentPdf />
                      ატესტატი
                    </a>
                  </Link>
                )}
              </ThumbInner>
            </Thumb>
            <Thumb>
              <ThumbInner>
                {user.school_document && (
                  <Link
                    href={`${process.env.NEXT_PUBLIC_STORAGE}${user.payment_document}`}
                  >
                    <a target="_blank">
                      <GrDocumentPdf />
                      სწავლის წლიური საფასურის 25% გადახდის დამადასტურებელი
                      ქვითარი
                    </a>
                  </Link>
                )}
              </ThumbInner>
            </Thumb>
            <Thumb>
              <ThumbInner>
                {user.military_accounting && (
                  <Link
                    href={`${process.env.NEXT_PUBLIC_STORAGE}${user.military_accounting}`}
                  >
                    <a target="_blank">
                      <GrDocumentPdf />
                      სამხედრო საბუთი
                    </a>
                  </Link>
                )}
              </ThumbInner>
            </Thumb>
          </FilesList>
        </div>
      </Content>
      <Export onClick={() => generateFile(data.id)}>
        <MdOutlinePrint size={18} />
        <span>Export</span>
      </Export>
    </Container>
  );
});

export default StudentCard;
