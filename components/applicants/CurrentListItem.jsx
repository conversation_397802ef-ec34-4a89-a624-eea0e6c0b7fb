import { useState, useEffect } from "react";
import styled from "styled-components";
import Link from "next/link";
import {
  lectureEdit,
  bookBlack,
  clockBlack,
  star,
  syllabus,
} from "../lecturer/lecturerSvg";

import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { MdKeyboardArrowDown } from "react-icons/md";
import BookModal from "./../student/library/BookModal";
import apiClientProtected from "../../helpers/apiClient";
import { map } from "lodash";

const CurrentListItem = ({ subject, openDetailsHandler, id }) => {
  const { locale } = useLocaleContext();
  const [headings, setHeadings] = useState([]);
  const [childHeadings, setChildHeadings] = useState([]);
  const [data, setData] = useState([]);
  const [dataColumns, setDataColumns] = useState([]);
  const [open, setOpen] = useState(false);
  const [missedLectures, setMissedLectures] = useState([]);

  useEffect(() => {
    const getTableData = async () => {
      // Parent headings array
      if (Object.entries(subject).length) {
        const responseHeadings =
          subject.headings &&
          Object.values(subject.headings)
            .filter((item) => !item.isChild)
            .map((item) => {
              if (item.column_name === "attendance1") {
                item.name_en = "attendance I";
                item.name_ka = "დასწრება I";
              } else if (item.column_name === "attendance2") {
                item.name_en = "attendance II";
                item.name_ka = "დასწრება II";
              } else if (item.column_name === "attendance3") {
                item.name_en = "attendance III";
                item.name_ka = "დასწრება III";
              }
              return item;
            });
        // Child headings array
        const childHeadings =
          subject.headings &&
          Object.values(subject.headings).filter((item) => item.isChild);

        setHeadings(responseHeadings);
        setChildHeadings(childHeadings);

        let dataObject = {};
        const arr = [];
        const responseColumns = [];
        responseColumns.push("missedLectures");
        dataObject["missedLectures"] =
          subject["missedLectures"] +
          " / " +
          subject["missedLecturesInPercent"] +
          "%";
        dataObject["missedLecturesInPercent"] = Number(
          subject["missedLecturesInPercent"]
        );

        for (let i = 0; i < subject.assignments.length; i++) {
          if (subject.assignments[i].hasChild) {
            for (
              let j = 0;
              j < subject.assignments[i].childAssignments.length;
              j++
            ) {
              responseColumns.push(
                subject.assignments[i].childAssignments[j].titleGeo + "_score"
              );
              responseColumns.push(
                subject.assignments[i].childAssignments[j].titleGeo + "_percent"
              );
            }
            responseColumns.push(subject.assignments[i].titleGeo + "_sum");
          } else {
            responseColumns.push(subject.assignments[i].titleGeo + "_score");
            responseColumns.push(subject.assignments[i].titleGeo + "_percent");
          }
        }

        setDataColumns(responseColumns);

        for (let i = 0; i < subject.assignments.length; i++) {
          if (subject.assignments[i].hasChild) {
            let totalScore = 0;
            for (
              let j = 0;
              j < subject.assignments[i].childAssignments.length;
              j++
            ) {
              dataObject[
                subject.assignments[i].childAssignments[j].titleGeo + "_score"
              ] = subject.assignments[i].childAssignments[j].takenScore;
              dataObject[
                subject.assignments[i].childAssignments[j].titleGeo + "_percent"
              ] =
                (
                  (subject.assignments[i].childAssignments[j].takenScore /
                    subject.assignments[i].childAssignments[j].score) *
                  100
                ).toFixed(2) + "%";

              totalScore +=
                subject.assignments[i].childAssignments[j].takenScore;
            }
            dataObject[subject.assignments[i].titleGeo + "_sum"] = totalScore;
          } else {
            dataObject[subject.assignments[i].titleGeo + "_score"] =
              subject.assignments[i].takenScore;
            dataObject[subject.assignments[i].titleGeo + "_percent"] =
              (
                (subject.assignments[i].takenScore /
                  subject.assignments[i].score) *
                100
              ).toFixed(2) + "%";
            // dataObject[subject.assignments[i].titleGeo + "_sum"] =
            //   subject.assignments[i].takenScore;
          }
          arr.push(dataObject);
          console.log(i, dataObject);
        }
        setData(arr);
      }
    };
    getTableData();
  }, []);

  const handleMissedLectures = async (syllabusId) => {
    try {
      const response = await apiClientProtected().get(
        `/administration/student-current-missed-hours/${id}/${syllabusId}`
      );
      console.log(response, "asdasddddddddddddddd");
      setMissedLectures(response.data.data);
      setOpen(true);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <>
      <Subject>
        <SubDescription>
          <div>
            <h4 onClick={() => openDetailsHandler(subject.id)}>
              {locale === "ka" ? subject.name : subject.name_en} -{" "}
              {subject.code} - ID: {subject.id}
              {data[0] && data[0].missedLecturesInPercent >= 33.34 && (
                <span className="failed-bedge">Failed</span>
              )}
            </h4>
            <div>
              <ul>
                {subject.lecturers?.map((item, index) => (
                  <li key={index}>
                    <p>
                      {item.first_name} {item.last_name}
                    </p>

                    {subject.lecturers.length > 1 && ", "}
                  </li>
                ))}
              </ul>
              <span></span>
              <ul>
                <li>
                  {star}
                  <p>
                    {" "}
                    {locale && langs[locale]["acts"]}: {subject.credits}
                  </p>
                </li>
                <li>
                  {clockBlack}
                  <p>
                    {locale && langs[locale]["hours"]}: {subject.totalHours}
                  </p>
                </li>
              </ul>
            </div>
          </div>
        </SubDescription>
        <Total isDetailsOpen={subject.all_score}>
          <div className="d-flex align-items-center gap-3">
            <h4>ჯამური ქულა</h4>
            <p>{subject.total}</p>
          </div>
        </Total>
      </Subject>

      <Details isDetailsOpen={subject.all_score}>
        <SubjectTabe>
          <thead>
            <tr>
              {headings?.map((item, index) => (
                <th colSpan={item.colSpan} rowSpan={item.rowSpan} key={index}>
                  {locale === "ka" ? item.name_ka : item.name_en}{" "}
                  {item.score && "-"} {item.score}{" "}
                  {item.score && langs[locale]["point"]}
                </th>
              ))}
            </tr>
            <tr>
              {childHeadings?.map((item, index) => (
                <th colSpan={item.colSpan} key={index}>
                  {locale === "ka" ? item.name_ka : item.name_en} - {item.score}{" "}
                  {langs[locale]["point"]}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr>
              {/* {JSON.stringify(data[0].missedLecturesInPercent)} */}
              {dataColumns.map((columnItem) => (
                <td
                  style={{
                    background: `${
                      columnItem === "missedLectures" &&
                      data[0].missedLecturesInPercent >= 33.34
                        ? "#e7526d"
                        : ""
                    }`,
                    color: `${
                      columnItem === "missedLectures" &&
                      data[0].missedLecturesInPercent >= 33.34
                        ? "white"
                        : ""
                    }`,
                  }}
                  key={columnItem}
                  onClick={() =>
                    columnItem === "missedLectures"
                      ? handleMissedLectures(subject.id)
                      : null
                  }
                >
                  {data[0][columnItem]}
                </td>
              ))}
            </tr>
          </tbody>
        </SubjectTabe>
      </Details>

      <BookModal
        setOpen={setOpen}
        open={open}
        title={locale && langs[locale]["title"]}
      >
        <MissedLectures>
          <table className="missed-table">
            <thead>
              <tr>
                <th>N</th>
                <th>თარიღი</th>
                <th>ლექცია</th>
              </tr>
            </thead>
            <tbody>
              {missedLectures?.map((item, index) => (
                <tr key={index}>
                  <td>{index + 1}</td>
                  <td>{item.date}</td>
                  <td>{item.nth_lecture}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </MissedLectures>
      </BookModal>
    </>
  );
};

const Subject = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #f6f9ff;
  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  align-items: center;
  padding: 6px 20px;
  overflow: hidden;
  margin-bottom: 2px;
  border-top: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  @media (max-width: 1245px) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const Details = styled.div`
  width: 100%;
  overflow-x: auto;
  background-color: #f6f9ff;
  margin-bottom: 1rem;

  :last-child {
    padding-bottom: ${(props) => (props.isDetailsOpen ? "0" : "0")};
  }
`;

const SubDescription = styled.div`
  display: flex;
  align-items: center;

  img {
    width: 46px;
    height: 46px;
    border-radius: 50%;
  }
  div {
    h4 {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      display: flex;
      align-items: center;
      letter-spacing: -0.03em;
      color: #953849;
      margin-bottom: 5px;
      @media (max-width: 560px) {
        font-size: 14px;
      }
    }
    .failed-bedge {
      color: #cd2525;
      margin-left: 16px;
      font-size: 14px;
      background: rgba(205, 37, 37, 0.2);
      padding: 2px 8px;
      border-radius: 4px;
    }
    a {
      color: #953849;
      font-weight: 700;
    }
    div {
      margin: 0;
      display: flex;
      align-items: center;
      p {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        display: flex;
        align-items: center;
        letter-spacing: -0.25px;
        color: #000000;
        transition: all 0.5s ease;
        /* :hover {
          font-weight: 600;
        } */
        @media (max-width: 560px) {
          font-size: 12px;
        }
      }
      display: flex;
      ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        span {
          margin: 0 12px 0 0;
          @media (max-width: 781px) {
            display: none;
          }
        }
        li {
          display: flex;
          align-items: center;
          margin-right: 12px;
          @media (max-width: 781px) {
            :nth-child(n + 4),
            :nth-child(2n + 3) {
              display: none;
            }
          }
          svg {
            path {
            }
            margin-right: 8px;
            @media (max-width: 560px) {
              width: 10px;
            }
          }
        }
      }
      span {
        height: 20px;
        width: 1px;
        display: block;
        background-color: #abb4c5;
        margin: 0 12px;
      }
    }
  }
`;

const Total = styled.div`
  max-width: 275px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 45px;
  h4 {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    display: flex;
    align-items: center;
    letter-spacing: -0.03em;
    color: #333333;
    padding: 12px 0;
  }
  p {
    font-family: "FiraGO";
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #333333;
  }
  @media (max-width: 1290px) {
    max-width: 230px;
  }
  @media (max-width: 1245px) {
    flex-direction: column;
    align-items: flex-start;
    max-width: 100%;
    padding: 0;
    div {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      :first-child {
        border-bottom: solid 1px #e2e3e9;
      }
    }
    h4 {
      display: block;
    }
  }
  button {
    border: none;
    outline: none;
    cursor: pointer;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 50%;
    border-radius: 50%;

    transition: all 0.5s ease;
    svg {
      transition: all 0.5s ease;
      transform: ${(props) =>
        props.isDetailsOpen ? "rotate(180deg)" : "rotate(0deg)"};
    }
  }
`;

const SubjectTabe = styled.table`
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #ccc;

  thead {
    @media (max-width: 1245px) {
      background-color: #e4e8f3;
    }
    background-color: #e4e8f3;
    tr {
      font-family: "FiraGO", sans-serif;
      font-weight: 600;
      font-weight: 500;
      font-size: 14px;
      line-height: 17px;
      text-align: left;
      color: #333333;
      th {
        padding: 12px 20px !important;
        text-align: center;
        border: 2px solid #ddd;
        @media (max-width: 1245px) {
          br {
            display: none;
            text-align: start;
          }
        }
      }
    }
  }
  tbody {
    background-color: #fff;
    @media (max-width: 1245px) {
      background-color: #fff;
    }
    tr {
      text-align: start;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 17px;
      color: #333333;
      td {
        text-align: center;
        border: 1px solid #eee;
        padding: 12px 20px !important;
        &:nth-child(1) {
          cursor: pointer;
        }
      }
    }
  }
`;

const MissedLectures = styled.div`
  .missed-table {
    margin-top: 28px;
    width: 100%;
    thead {
      background-color: #953849;
      tr {
        th {
          padding: 16px !important;
          font-family: "FiraGO", sans-serif;
          font-style: normal;
          font-weight: 700;
          text-align: center;
          font-size: 16px;
          line-height: 19px;
          letter-spacing: -0.03em;
          color: #ffffff;
          :last-child {
            text-align: right;
          }
        }
      }
    }
    tbody {
      tr {
        border-bottom: 1px solid #f3f3f3 !important;
      }
      td {
        padding: 16px !important;
        background-color: #dde5f8;
        font-family: "FiraGO", sans-serif;
        font-style: normal;

        font-weight: 500;
        font-size: 14px;
        line-height: 17px;
        letter-spacing: -0.03em;
        color: #333333;
        text-align: center;
        :last-child {
          text-align: right;
        }
      }
    }
  }
`;

export default CurrentListItem;
