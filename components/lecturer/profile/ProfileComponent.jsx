import { useState, useEffect } from "react";
import styled from "styled-components";
import Image from "next/image";
import LecturerProfileNavigation from "./../../../components/profile/LecturerProfileNavigation";
import profileblack from "../../../public/assets/media/profile-black.svg";
import mobile from "../../../public/assets/media/mobile-lecturer.svg";
import { profile } from "../../../components/ui/Header/headerSvg";

import {
  edit,
  surname,
  send,
  profileMob,
  dateOfBirth,
  addres,
} from "../../../components/profile/profileSvg";

import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";
import { arrowLeft } from "../../../components/profile/profileSvg";
import apiClientProtected from "../../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import ButtonLoader from "../../ui/ButtonLoader";

const ProfileComponent = () => {
  const [showNavigation, setShowNavigation] = useState(false);
  const { locale } = useLocaleContext();
  const [navigation, setNavigation] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldData, setFieldData] = useState({
    name: "",
    surname: "",
    phone: "",
    email: "",
    photo: "",
    address: "",
    birthday: "",
  });

  const [touched, setTouched] = useState({
    phone: false,
    address: false,
  });

  useEffect(() => {
    const getUser = async () => {
      try {
        const response = await apiClientProtected().get("/lecturer/profile");
        setFieldData(response.data);
      } catch (err) {
        console.log(err);
      }
    };
    getUser();
  }, []);

  const validate = () => {
    let errors = {};

    if (!fieldData.phone) {
      errors.phone = "Phone is required";
    } else if (fieldData.phone.length !== 9) {
      errors.phone = "Phone number must be 9 characters long";
    }

    if (!fieldData.address) {
      errors.address = "Address  is required";
    }

    return errors;
  };

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const navigationHandler = () => {
    setShowNavigation(!showNavigation);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const fd = new FormData();
    setIsSubmitting(true);

    const touchedCopy = { ...touched };
    for (let key in touchedCopy) {
      if (validate()[key]) {
        touchedCopy[key] = true;
      }
    }
    setTouched(touchedCopy);

    if (Object.entries(validate()).length) {
      setIsSubmitting(false);
      return;
    }

    fd.append("phone", fieldData.phone);
    fd.append("address", fieldData.address);
    fd.append("_method", "PUT");

    try {
      const response = await apiClientProtected().post("/lecturer/profile", fd);
      console.log(response);
      setSuccess(true);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
    }
  };

  return (
    <LecturerPage>
      <MobNavigation showNavigation={showNavigation}>
        <LecturerProfileNavigation navigationHandler={navigationHandler} />
      </MobNavigation>
      <MainContainer>
        <GoBack onClick={navigationHandler}>{arrowLeft}</GoBack>
        <Intro>
          <span>
            <Image
              src={
                fieldData.photo && fieldData.photo.includes(".")
                  ? fieldData.photo
                  : "/assets/media/avatars/blank.png"
              }
              width={153}
              height={153}
            />
            {/* <button>{edit}</button> */}
          </span>
          <ul>
            <li>
              {fieldData.name} {fieldData.surname}
            </li>
            <li>{fieldData.email}</li>
            <li>{fieldData.phone}</li>
          </ul>
        </Intro>
        <h3>პროფილი</h3>
        <form onSubmit={handleSubmit}>
          <div>
            <span>
              <InputDiv disabled={true}>
                {profile}
                <input
                  type="text"
                  disabled
                  defaultValue={fieldData.name}
                  placeholder="სახელი"
                />
              </InputDiv>
              <InputDiv disabled={true}>
                {surname}
                <input
                  type="text"
                  disabled
                  defaultValue={fieldData.surname}
                  placeholder="გვარი"
                />
              </InputDiv>
            </span>
            <span></span>
            <InputDiv disabled={true}>
              {send}
              <input
                type="text"
                disabled
                defaultValue={fieldData.email}
                placeholder={locale && langs[locale]["email"]}
              />
            </InputDiv>
            <span>
              <FormField>
                <InputDiv>
                  {profileMob}
                  <input
                    type="text"
                    name="phone"
                    value={fieldData.phone}
                    onChange={handleChange}
                    onBlur={(e) =>
                      setTouched({ ...touched, [e.target.name]: true })
                    }
                    placeholder={locale && langs[locale]["phone"]}
                  />
                </InputDiv>
                {validate() && validate().phone && touched.phone ? (
                  <Error>{validate().phone}</Error>
                ) : (
                  ""
                )}
              </FormField>

              <InputDiv disabled={true}>
                {dateOfBirth}
                <input
                  type="text"
                  disabled
                  defaultValue={fieldData.birthday}
                  placeholder="დაბადების თარიღი"
                />
              </InputDiv>
            </span>
            <FormField>
              <InputDiv>
                {addres}
                <input
                  type="text"
                  name="address"
                  value={fieldData.address}
                  onChange={handleChange}
                  onBlur={(e) =>
                    setTouched({ ...touched, [e.target.name]: true })
                  }
                  placeholder={locale && langs[locale]["address"]}
                />
              </InputDiv>
              {validate() && validate().address && touched.address ? (
                <Error>{validate().address}</Error>
              ) : (
                ""
              )}
            </FormField>
          </div>

          <button>
            {isSubmitting ? <ButtonLoader /> : locale && langs[locale]["save"]}
          </button>

          {/* <AdditionalInfo>
            <h4>მიმართულება</h4>
            <ul>
              <li>სამართალი</li>
            </ul>
            <h4>განათლება</h4>
            <ul>
              <li>
                2006 - 2010 ბაკალავრი ივანე ჯავახიშვილის სამართლის ფაკულტეტი
              </li>
              <li>
                2010 - 2012 მაგისტრატურა ივანე ჯავახიშვილის სამართლის ფაკულტეტი
              </li>
            </ul>
            <h4>სამუშაო გამოცდილება</h4>
            <ul>
              <li>
                2018 - 2022 სამართლის ლექტორი საქართველოს საზოგადოებრივ საქმეთა
                ინსტიტუტი
              </li>
              <li>
                2018 - 2022 მოწვეული ლექტორი ივანე ჯავახიშვილის სახელმწიფო
                უნივერსიტეტში{" "}
              </li>
            </ul>
          </AdditionalInfo> */}
        </form>
      </MainContainer>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </LecturerPage>
  );
};

export default ProfileComponent;

const MobNavigation = styled.div`
  max-width: 220px;
  width: 100%;
  @media (max-width: 1080px) {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;
    width: 100%;
    height: 100%;
    z-index: 10;
    padding: 25px;
    transform: translateX(-100%);
    transform: ${(props) =>
      props.showNavigation ? "translateX(0%)" : `translateX(100%)`};
    transition: all 0.5s ease-in-out;
    div {
      max-width: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      z-index: 20;
      ul {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
`;

const InputDiv = styled.div`
  width: 100%;
  padding: 0 20px;
  height: 64px;
  border: 1px solid #261747;
  border-radius: 20px;
  background: ${({ disabled }) => (disabled ? "#f7f7f7" : "#fff")};
  outline: none;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  input {
    margin-left: 15px;
    width: 100%;
    ::placeholder {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #757171;
      @media (max-width: 1180px) {
        font-size: 14px;
      }
    }
    :disabled {
      background-color: #f7f7f7;
    }
  }
`;

const LecturerPage = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 25px;
  position: relative;
  overflow: hidden;
  @media (max-width: 1080px) {
    padding-left: 250px;
  }
  @media (max-width: 980px) {
    padding-left: 25px;
  }
`;
const GoBack = styled.button`
  position: absolute;
  top: 30px;
  left: 20px;
  display: none;
  @media (max-width: 1080px) {
    display: block;
  }
`;
const MainContainer = styled.div`
  background-color: #ffffff;
  border-radius: 10px;
  width: 100%;
  min-height: 100vh;
  margin-left: 10px;
  height: 100%;
  padding: 25px;
  h3 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #333333;
    text-align: center;
    margin-top: 5px;
    display: none;
    @media (max-width: 1080px) {
      display: block;
    }
  }
  form {
    max-width: 555px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 25px;
    @media (max-width: 1080px) {
      max-width: 100%;
      margin-top: 47px;
    }
    div {
      width: 100%;
    }
    span {
      display: flex;
      @media (max-width: 1080px) {
        flex-direction: column;
      }
      div {
        :first-child {
          margin-right: 10px;
        }
      }
    }
    button {
      border: solid 1px transparent;
      border-radius: 23px;
      background-color: #e7526d;
      width: 160px;
      height: 47px;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 16px;
      line-height: 19px;
      text-align: center;
      color: #ffffff;
      transition: all 0.5s ease;
      :hover {
        background-color: #e08999;
      }
    }
  }
`;

const Intro = styled.div`
  display: flex;
  align-items: center;
  @media (max-width: 1080px) {
    display: none;
  }
  span {
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    svg {
      position: absolute;
      bottom: 14px;
      right: 0;
    }
  }
  ul {
    margin-left: 40px;
    li {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #333333;
      margin-bottom: 10px;
    }
  }
`;
const Form = styled.form`
  width: 100%;
  ul {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 565px;
    width: 100%;
    li {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      width: 100%;
      :first-child {
        display: flex;
        justify-content: space-between;
        div {
          max-width: 49%;
          width: 100%;
        }
      }
      div {
        padding: 20px 15px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border: solid 1px #261747;
        border-radius: 20px;
        max-height: 64px;
        height: 100%;
        width: 100%;
        img {
          margin-right: 5px;
        }
      }
    }
  }
`;
const AdditionalInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  h4 {
    margin: 25px 0 15px 0;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    color: #333333;
  }
  ul {
    li {
      list-style: disc;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      color: #333333;
      margin: 0 0 15px 20px;
    }
  }
`;

const FormField = styled.div`
  display: block;
`;

const Error = styled.div`
  display: block;
  color: red;
  margin-bottom: 8px;
`;
