import { useState } from "react";
import { apiClient } from "./../../helpers/apiClient";
import { useRouter } from "next/router";
import BaseInput from "../base/BaseInput";
import Image from "next/image";
import styled from "styled-components";
import logo from "/public/assets/media/logo.svg";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import logindec from "/public/assets/media/logindecoration.svg";
import georgia from "/public/assets/media/georgialogo.svg";
import uk from "/public/assets/media/uk.svg";
import SweetAlert2 from "react-sweetalert2";
import { LANG_LIST } from "./../projectData";
import { resetFields } from "./../input_fields/authFields";
import ButtonLoader from "./../ui/ButtonLoader";

const ResetForm = ({ token }) => {
  const router = useRouter();
  const { locale, handleLocale } = useLocaleContext();
  const [passwords, setPasswords] = useState({
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [fields, setFields] = useState(resetFields);
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setPasswords({ ...passwords, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    fd.append("password", passwords.password);
    fd.append("password_confirmation", passwords.confirmPassword);
    fd.append("token", token);

    try {
      const response = await apiClient().post("/auth/reset-password", fd);
      console.log(response.data);

      setSuccess(true);
      setSwalProps({
        show: true,
        title: "",
        text: "პაროლი წარმატებით შეიცვალა",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      setIsSubmitting(false);
      router.push("/login");
    } catch (error) {
      setIsSubmitting(false);
      setErrors(error.response.data);
      console.log(error.response.data);
    }

    // const userData = JSON.parse(localStorage.getItem("user"));
  };

  return (
    <>
      <NewPassword>
        <FormDiv>
          <Logo>
            <Image src={logo} alt="logo" />
          </Logo>
          <form onSubmit={handleSubmit}>
            <h2>{locale && langs[locale]["password_recovery"]}</h2>
            {fields.map((field) => (
              <span>
                <BaseInput
                  key={field.id}
                  type={field.type}
                  name={field.name}
                  errorMessage={field.errorMessage}
                  value={passwords[field.name]}
                  handleChange={handleChange}
                  label={locale && langs[locale][field.label]}
                  decoration={field.image}
                  placeholder={locale && langs[locale][field.placeholder]}
                />
                <div className="text-danger">
                  {errors && errors[field.name]}
                </div>
              </span>
            ))}

            <button type="submit">
              {isSubmitting ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["save"]
              )}
            </button>
            <Flags>
              {LANG_LIST.map((item, index) => (
                <div
                  className="lang-icon"
                  onClick={() => handleLocale(item)}
                  key={index}
                >
                  <img src={item.image} alt="flag" />
                </div>
              ))}
            </Flags>
          </form>
        </FormDiv>
        <DecorationDiv>
          <Image src={logindec} alt="deocration" />
        </DecorationDiv>
      </NewPassword>

      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
};

export default ResetForm;

const NewPassword = styled.div`
  width: 100%;
  height: 100vh;
  background-color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 1080px) {
    flex-direction: column;
  }
`;
const DecorationDiv = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  @media (max-width: 1080px) {
    display: none;
  }
  img {
    width: 65%;
    object-fit: cover;
  }
`;
const FormDiv = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 40%;
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 10px;
  @media (max-width: 1080px) {
    max-width: 100%;
  }
  form {
    border-radius: 10px;
    background: #ffffff;
    box-shadow: 0px 1px 8px rgba(28, 40, 69, 0.1);
    border-radius: 10px;
    padding: 35px 25px;
    max-width: 622px !important;
    width: 100%;
    @media (max-width: 1080px) {
      max-width: 100%;
    }
    @media (max-width: 654px) {
      box-shadow: none;
    }
    @media (max-width: 500px) {
      padding: 35px 0;
    }
    h2 {
      text-align: center;
      margin: 35px 0 40px 0;
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 600;
      font-size: 24px;
      line-height: 29px;
      color: #953849;
    }
    div {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 15px;
      @media (max-width: 645px) {
        margin-bottom: 20px;
      }
      p {
        font-family: "FiraGO", sans-serif;
        font-style: normal;
        font-weight: 350;
        font-size: 15px;
        line-height: 22px;
        color: #242323;
      }
      span {
        color: #7ea4ff;
        text-decoration: underline solid 1px #7ea4ff;
        margin-left: 5px;
        font-size: 15px;
        font-family: "FiraGO", sans-serif;
      }
    }
    label {
      font-family: "FiraGO";
      font-style: normal;
      font-weight: 400;
      font-size: 15px;
      line-height: 22px;
      display: flex;
      align-items: center;
      color: #242323;
      margin-bottom: 15px;
    }
    button {
      background-color: #7ea4ff;
      padding: 20px;
      width: 100%;
      height: 58px;
      border: none;
      outline: none;
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 500;
      font-size: 15px;
      line-height: 22px;
      color: #fff;
      border-radius: 10px;
      cursor: pointer;
      @media (max-width: 645px) {
        padding: 24px;
      }
      :last-child {
        display: flex;
        align-items: center;
        justify-content: center;
        border: solid 1px #6f6f6f;
        background: transparent;
        color: #596270;
        margin: 0;
        img {
          margin: 0;
        }
        p {
          margin-left: 30px;
        }
      }
    }
  }
`;
const Logo = styled.div`
  margin: 30px 0 40px 0;
`;
const Input = styled.div`
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  display: flex;
  align-items: center;
  color: #6f6f6f;
  width: 100%;
  padding: 0px 27px;
  background: #f6f6f6;
  border: none;
  outline: none;
  border-radius: 10px;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 30px;
  @media (max-width: 645px) {
    margin-bottom: 30px;
    padding: 0 24px;
  }
  input {
    width: 100%;
    padding: 25px 0 25px 13px;
    border: none;
    outline: none;
    background: transparent;
    @media (max-width: 645px) {
      padding: 24px 0 24px 13px;
    }
  }
`;

const Flags = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center !important;
  margin: 20px 0;
  gap: 15px;
  .lang-icon {
    width: 60px;
    cursor: pointer;
    border: 1px solid #eee;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
    img {
      width: 100%;
    }
  }
`;
