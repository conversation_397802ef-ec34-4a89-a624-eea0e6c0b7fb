import { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/router";
import Image from "next/image";
import Head from "next/head";
import {
  MdOutlineChevronRight,
  MdExpandMore,
  MdAdd,
  MdSettings,
  MdCheck,
  MdDeleteOutline,
  MdClose,
  MdOutlineRemove,
} from "react-icons/md";
import { FaChalkboardTeacher, FaBookOpen } from "react-icons/fa";
import { MdPeopleOutline } from "react-icons/md";
import DatePicker from "react-datepicker";
import { useTableContext } from "../context/TableContext";
import apiClientProtected from "../../helpers/apiClient";
import { dateFormat } from "../../helpers/funcs";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import SignsRow from "./SignsRow";
import Modal from "../ui/Modal";
import "react-datepicker/dist/react-datepicker.css";
import excel from "/public/assets/media/excel.svg";
import AddStudentForm from "../forms/AddStudentForm";

const SIGNS_DATA = [
  { id: 1, title: "დადასტარება" },
  { id: 2, title: "არ გამოცხადდა" },
  { id: 3, title: "არ დაადასტურა" },
  { id: 4, title: "გადაბარება" },
];

const HseSignsTable = ({ SignsId, type }) => {
  const { locale } = useLocaleContext();
  const router = useRouter();
  const [signsDate, setSignsDate] = useState("");
  const [modalType, setModalType] = useState("");
  const [syllabusId, setSyllabusId] = useState(null);
  const [formUrl, setFormUrl] = useState("");
  const [lectureDates, setLectureDates] = useState([]);
  const [headings, setHeadings] = useState([]);
  const [students, setStudents] = useState([]);
  const {
    openModal,
    setOpenModal,
    alertMessage,
    setAlertMessage,
    setShrinkSidebar,
    data,
    setData,
    signsDataUpdate,
  } = useTableContext();

  useEffect(() => {
    (async () => {
      const date = signsDate
        ? dateFormat(signsDate, null, "-").split("-").reverse().join("-")
        : "";
      const queryObject = { ...router.query, lecture_date: date };
      delete queryObject.id;
      let url = `/syllabus/students/${SignsId}?`;
      for (let key in queryObject) {
        url += `&${key}=${queryObject[key]}`;
      }

      console.log(router, "console router");

      setFormUrl(url);
      const response = await apiClientProtected().get(url);
      setSyllabusId(response.data.syllabusId);
      const lectureDatesArray = Object.values(response.data.lectureDates).map(
        (item) => new Date(item)
      );
      setLectureDates(lectureDatesArray);

      setData(response.data);
      setHeadings([
        ...response.data.headings,
        {
          name_ka: "მოქმედება",
          name_en: "Action",
          column_name: "action",
          row_span: 2,
        },
      ]);
      setStudents(response.data.students);
    })();
  }, [signsDate, signsDataUpdate]);

  const handleExport = () => {};

  const handleAttendance = async (id, nth_lecture, lecture_id) => {
    console.log(id, nth_lecture);
    const fd = new FormData();
    fd.append("student_id", id);
    fd.append("nth_lecture", nth_lecture);

    try {
      const response = await apiClientProtected().post(
        `/syllabus/lecture/set-student-attendance/${data.lectureId}`,
        fd
      );

      const ddt = students.map((item) => {
        if (item.studentId === id) {
          item[`attendance${nth_lecture}`] = !item[`attendance${nth_lecture}`];
          if (item[`attendance${nth_lecture}`]) {
            item.missedLectures += 1;
          } else {
            item.missedLectures -= 1;
          }
          item.missedLecturesInPercent = (
            (item.missedLectures / data.hours) *
            100
          ).toFixed(2);
        }

        return item;
      });
      setStudents(ddt);

      console.log(response);
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
    } catch (err) {
      console.log(err);
    }
  };

  const handleMark = async (
    assignment_id,
    student_id,
    last_checkout_status_id
  ) => {
    const fd = new FormData();
    fd.append("assignment_id", assignment_id);
    fd.append("student_id", student_id);
    fd.append("last_checkout_status_id", last_checkout_status_id);
    fd.append("is_percent", 0);
    fd.append("point", 0);

    console.log(data.syllabusId);

    try {
      const response = await apiClientProtected().post(
        `/syllabus/set-student-mark/${data.syllabusId}`,
        fd
      );
      const ddt = students.map((item) => {
        for (let i = 0; i < item.assignments.length; i++) {
          for (let k = 0; k < item.assignments[i].lastCheckout.length; k++) {
            if (
              item.studentId === student_id &&
              item.assignments[i].assignmentId === assignment_id &&
              item.assignments[i].lastCheckout[k].id === last_checkout_status_id
            ) {
              console.log(item.assignments[i].lastCheckout[k], "ONE");
              item.assignments[i].lastCheckout[k].date = !item.assignments[i]
                .lastCheckout[k].date
                ? 1
                : null;
              item.assignments[i].lastCheckoutFinal[0].id =
                last_checkout_status_id;
              item.assignments[i].lastCheckoutFinal[0].date = 1;
            }
          }
        }

        return item;
      });

      console.log(ddt);

      setStudents(ddt);
    } catch (err) {
      console.log(err);
    }
  };

  const handleDelete = async (student_id) => {
    console.log(student_id);
    try {
      const response = await apiClientProtected().post(
        "/curriculum/remove-students",
        { student_id, syllabus_id: Number(syllabusId) }
      );

      const mapped = students.filter((item) => item.studentId !== student_id);
      setStudents(mapped);
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <>
      <Head>
        <title>{data && data.subject}</title>
      </Head>
      {data && type !== "lecturer" && (
        <TableHeader>
          <div>
            <span>მთავარი</span>
            <MdOutlineChevronRight color="#a1a5b7" />
            <span>აკადემიური მოსწრება</span>
            <MdOutlineChevronRight color="#a1a5b7" />
            <span>{data.current_semester}</span>
          </div>

          <ButtonController>
            {type !== "lecturer" && (
              <button
                onClick={() => {
                  setOpenModal(true);
                  setModalType("students");
                }}
              >
                <span>
                  <MdPeopleOutline color="#00733B" size={18} />
                </span>
                სტუდენტის დამატება
              </button>
            )}
            {type !== "lecturer" && (
              <button
                onClick={() => {
                  setOpenModal(true);
                  setModalType("exams");
                }}
              >
                <span>
                  <MdSettings color="#00733B" size={18} />
                </span>
                პარამეტრები
              </button>
            )}
            {type !== "lecturer" && (
              <button
                onClick={() => {
                  setOpenModal(true);
                  setModalType("import");
                }}
              >
                <Image src={excel} />
                იმპორტი
              </button>
            )}

            <button onClick={handleExport}>
              <Image src={excel} /> ექსპორტი
            </button>
          </ButtonController>
        </TableHeader>
      )}

      <Wrapper>
        {data && (
          <>
            <TableTitle>
              <DateElement>
                <DatePicker
                  calendarStartDay={1}
                  className="form-control mb-3 example-custom-input"
                  selected={signsDate}
                  highlightDates={[
                    {
                      "react-datepicker__day--highlighted": lectureDates,
                    },
                  ]}
                  placeholderText="Select a date"
                  includeDates={lectureDates}
                  onChange={(date) => setSignsDate(date)}
                  dateFormat="dd/MM/yyyy"
                />
              </DateElement>

              <div className="course-title">
                <h3>{data.subject}</h3> -
                <h3>
                  {data.course} {locale && langs[locale]["course"]}
                </h3>{" "}
                -
                {data.studentGroups && data.studentGroups.length ? (
                  data.studentGroups.map((item) => <h3>{item.name},</h3>)
                ) : (
                  <h3>N/A</h3>
                )}
              </div>
              <div className="multi-select-wrapper"></div>
            </TableTitle>

            <LectureWrapper>
              {data.lecturers?.map((item) => (
                <div className="lecturer-field">
                  <FaChalkboardTeacher size={20} />
                  {item.first_name} {item.last_name}
                </div>
              ))}
            </LectureWrapper>
          </>
        )}
        <MainContainer>
          <table>
            <thead>
              <tr>
                {headings.map((item, index) => (
                  <th
                    key={index}
                    colSpan={item.colSpan}
                    onClick={() => sortedArray(item.column_name)}
                  >
                    {item.name_ka}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {students.map((item, index) => (
                <tr key={index}>
                  <td>{index + 1}</td>
                  <td>{item.student}</td>
                  <td>
                    <TotalCheck attendance={item.total}>
                      <div>
                        {item.total ? (
                          <MdCheck color="#fff" />
                        ) : (
                          <MdClose color="#fff" />
                        )}
                      </div>
                    </TotalCheck>
                  </td>
                  <td>
                    {item.missedLectures} / {item.missedLecturesInPercent}%
                  </td>
                  {headings.filter((item) => item.column_name === "attendance1")
                    .length ? (
                    <>
                      <td>
                        <AttendanceCheck attendance={item[`attendance1`]}>
                          <div
                            onClick={() => handleAttendance(item.studentId, 1)}
                          >
                            {item[`attendance1`] ? (
                              <MdOutlineRemove color="#fff" />
                            ) : (
                              <MdAdd color="#fff" />
                            )}
                          </div>
                        </AttendanceCheck>
                      </td>
                      <td>
                        <AttendanceCheck attendance={item[`attendance2`]}>
                          <div
                            onClick={() => handleAttendance(item.studentId, 2)}
                          >
                            {item[`attendance2`] ? (
                              <MdOutlineRemove color="#fff" />
                            ) : (
                              <MdAdd color="#fff" />
                            )}
                          </div>
                        </AttendanceCheck>
                      </td>
                    </>
                  ) : null}
                  {headings.filter((item) => item.column_name === "attendance3")
                    .length ? (
                    <td>
                      <AttendanceCheck attendance={item[`attendance3`]}>
                        <div
                          onClick={() => handleAttendance(item.studentId, 3)}
                        >
                          {item[`attendance3`] ? (
                            <MdOutlineRemove color="#fff" />
                          ) : (
                            <MdAdd color="#fff" />
                          )}
                        </div>
                      </AttendanceCheck>
                    </td>
                  ) : null}
                  {headings.filter((item) => item.column_name === "attendance4")
                    .length ? (
                    <td>
                      <AttendanceCheck attendance={item[`attendance4`]}>
                        <div
                          onClick={() => handleAttendance(item.studentId, 4)}
                        >
                          {item[`attendance4`] ? (
                            <MdOutlineRemove color="#fff" />
                          ) : (
                            <MdAdd color="#fff" />
                          )}
                        </div>
                      </AttendanceCheck>
                    </td>
                  ) : null}
                  {item.assignments.map((assign, index) => (
                    <>
                      <td>
                        <div className="d-flex justify-content-center gap-4">
                          {assign.lastCheckout.map((sign, index) => (
                            <TableCellCheck
                              key={index}
                              id={sign.id}
                              isActive={sign.date}
                            >
                              <div
                                onClick={() =>
                                  handleMark(
                                    assign.assignmentId,
                                    item.studentId,
                                    sign.id
                                  )
                                }
                              >
                                {sign.id === 1 ? (
                                  <MdCheck color="#fff" />
                                ) : sign.id === 2 ? (
                                  <MdOutlineRemove color="#fff" />
                                ) : sign.id === 3 ? (
                                  <MdClose color="#fff" />
                                ) : (
                                  <MdCheck color="#fff" />
                                )}
                              </div>
                              {sign[`name_${locale}`]}
                            </TableCellCheck>
                          ))}
                        </div>
                      </td>
                      <td>
                        <TableCellCheck
                          key={index}
                          isActive={assign.lastCheckoutFinal[0].date}
                          id={assign.lastCheckoutFinal[0].id}
                        >
                          <div>
                            {assign.lastCheckoutFinal[0].id === 1 ? (
                              <MdCheck color="#fff" />
                            ) : assign.lastCheckoutFinal[0].id === 2 ? (
                              <MdOutlineRemove color="#fff" />
                            ) : assign.lastCheckoutFinal[0].id === 3 ? (
                              <MdClose color="#fff" />
                            ) : (
                              <MdCheck color="#fff" />
                            )}
                          </div>
                          დადასტურება
                        </TableCellCheck>
                      </td>
                    </>
                  ))}
                  <td key={index} data-index={index}>
                    <DeleteButton
                      title={locale && langs[locale]["delete"]}
                      onClick={() => handleDelete(item.studentId)}
                    >
                      <MdDeleteOutline size={18} />
                    </DeleteButton>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </MainContainer>
      </Wrapper>

      {openModal && (
        <Modal
          title={
            modalType === "import" && locale
              ? langs[locale]["import"]
              : modalType === "students" && locale
              ? langs[locale]["students"]
              : langs[locale]["settings"]
          }
          modalSize={true}
        >
          {modalType === "import" && <SignsImportForm id={syllabusId} />}
          {modalType === "exams" && <ExamForm id={syllabusId} />}
          {modalType === "students" && (
            <AddStudentForm syllabusId={syllabusId} url={formUrl} />
          )}
        </Modal>
      )}
    </>
  );
};

export default HseSignsTable;

const Wrapper = styled.div`
  padding-left: 2rem;
  padding-right: 2rem;
`;

const MainContainer = styled.div`
  margin: 0 auto 2rem;
  width: 100%;
  overflow-x: auto;
  max-height: 650px;
  box-shadow: 10px 10px 8px rgb(0 0 0 / 10%);
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  // &::-webkit-scrollbar-track {
  //   background: red;
  // }

  ::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  :hover::-webkit-scrollbar-thumb {
    background-color: #41416e;
  }
  @media (max-width: 1440px) {
    max-height: 500px;
  }
  table {
    border-collapse: collapse;
    min-width: max-content;
    width: 100%;
  }
  table th {
    position: sticky;
    top: 0px;
    left: 0px;
    background: #d5dae9;
    color: #333333;
  }
  table tr {
    background: #f6f9ff;
    &:hover td {
      background: #ebf0f9;
    }
  }
  table tr:nth-child(even) {
    background: #ebf0f9;
    td:nth-child(1) {
      background: #ebf0f9;
    }
    td:nth-child(2) {
      background: #ebf0f9;
    }
    td:nth-child(3) {
      background: #ebf0f9;
    }
    td:nth-child(4) {
      background: #ebf0f9;
    }
  }
  table tr:first-child th:nth-child(1) {
    position: sticky;
    left: 0px;
    top: 0px;
    z-index: 7;
  }

  table tr:first-child th:nth-child(2) {
    position: sticky;
    left: 38.5px;
    top: 0px;
    z-index: 7;
    width: 250px;
    cursor: pointer;
  }
  table tr:first-child th:nth-child(3) {
    position: sticky;
    left: 289px;
    top: 0px;
    z-index: 7;
    cursor: pointer;
  }
  table tr:first-child th:nth-child(4) {
    position: sticky;
    left: 344px;
    top: 0px;
    z-index: 7;
    cursor: pointer;
  }

  table td:nth-child(1) {
    position: sticky;
    left: 0px;
    z-index: 2;
    background: #f6f9ff;
  }
  table td:nth-child(2) {
    position: sticky;
    left: 38.5px;
    z-index: 2;
    background: #f6f9ff;
  }
  table td:nth-child(3) {
    position: sticky;
    left: 289px;
    z-index: 2;
    background: #f6f9ff;
  }
  table td:nth-child(4) {
    position: sticky;
    left: 344px;
    z-index: 2;
    background: #f6f9ff;
  }
  table th,
  table td {
    padding: 0.5rem 1rem;
    border: 1px solid #fff;
    text-align: center;
  }
  table th {
    z-index: 6;
  }

  input {
    padding: 0.5rem;
    width: 50px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
`;

const TableTitle = styled.div`
  margin-top: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 0;

  .course-title {
    display: flex;
    gap: 8px;
    align-items: center;
    h3 {
      color: #953849;
      font-size: 1.25rem;
    }
  }
  .multi-select-wrapper {
    width: 15%;
  }
`;

const DateElement = styled.div`
  /* padding: 0 2rem; */
  position: relative;
  width: 15%;
  display: flex;
  svg {
    position: absolute;
    z-index: 1;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
  }
  select {
    width: 100%;
  }
`;

const NoData = styled.div`
  width: 100%;
  height: calc(100vh - 66px);
  display: flex;
  justify-content: center;
  align-items: center;
`;

const LectureWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 1rem;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  .lecturer-field {
    display: flex;
    gap: 8px;
    align-items: center;
  }
`;

const TableCellCheck = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  div {
    width: 25px;
    height: 25px;
    display: flex;
    opacity: 1;
    justify-content: center;
    align-items: center;
    background: ${({ isActive, id }) =>
      isActive && id === 1
        ? "#0abe28"
        : isActive && id === 2
        ? "#c91616"
        : isActive && id === 3
        ? "#f0ad4e"
        : isActive && id === 4
        ? "#0a58be"
        : "#a0b3eb"};
    border-radius: 50%;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
`;

const AttendanceCheck = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: ${({ attendance }) => (!attendance ? "#5bc0de" : "#ff8686")};
    border-radius: 50%;
    cursor: pointer;
  }
  span {
    color: #fff;
  }
`;

const TotalCheck = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: ${({ attendance }) => (attendance ? "#2CBE29" : "#CD2525")};
    border-radius: 50%;
    cursor: pointer;
  }
  span {
    color: #fff;
  }
`;

const TableHeader = styled.div`
  background: #fff;
  padding: 1rem 2rem;
  justify-content: space-between;
  border-top: 1px solid #eee;
  display: flex;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  div {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  span {
    color: #7f849e !important;
    font-weight: 600;
  }
`;

const ButtonController = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    padding: 13px 10px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: -0.25px;
    color: #333333;
    transition: all 0.5s ease;
    background-color: #ffffff;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
      0px 0px 1px rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    margin-left: 15px;
    @media (max-width: 1080px) {
      max-width: 105px;
      margin-left: 5px;
    }
    @media (max-width: 760px) {
      max-width: 100%;
      width: 100%;
      margin-bottom: 10px;
    }

    :nth-child(2) {
      max-width: 400px;
      @media (max-width: 760px) {
        max-width: 100%;
        width: 100%;
      }
    }
  }
`;
const DeleteButton = styled.div`
  width: 36px;
  height: 36px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin: auto;
  cursor: pointer;
  transition: all 300ms;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  &:hover {
    background: #f7f7f7;
  }
`;
