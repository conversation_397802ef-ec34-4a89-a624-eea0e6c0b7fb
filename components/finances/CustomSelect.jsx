import { Select } from "antd";
import "antd/dist/antd.css";
import styled from "styled-components";

const CustomSelect = (props) => {
  const onChange = () => {
    console.log(`selected`);
  };

  const onSearch = () => {
    console.log("search:");
  };
  return (
    <Element
      showSearch
      placeholder={props.placeholder}
      optionFilterProp="children"
      onChange={onChange}
      onSearch={onSearch}
      filterOption={(input, option) =>
        (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
      }
      options={props.data}
    />
  );
};

const Element = styled(Select)`
  width: 100%;
  .ant-select-selector {
    border: none !important;
    background-color: #d6e4e5;
  }
`;

export default CustomSelect;
