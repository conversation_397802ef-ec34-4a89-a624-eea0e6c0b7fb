import { useState, useRef, useEffect } from "react";
import { useTableContext } from "../context/TableContext";
import styled from "styled-components";
import { FormElement, FormItem, FlexElement } from "./styles";
import { MdCloudUpload } from "react-icons/md";
import SubmitLoader from "../ui/SubmitLoader";
import { RiFileExcel2Fill } from "react-icons/ri";
import apiClientProtected from "./../../helpers/apiClient";
import { langs } from "../locale";
// import BaseDropzone from "./../base/BaseDropzone";
import { useLocaleContext } from "../context/LocaleContext";

const ImportForm = () => {
  const { errors, setErrors, setOpenModal, pageInfo, setData } =
    useTableContext();
  const { locale } = useLocaleContext();
  const [fieldData, setFieldData] = useState({
    school_id: "",
    program_id: "",
    flow_id: "",
    excelFile: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [percent, setPercent] = useState(0);
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [flows, setFlows] = useState([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [usersList, setUsersList] = useState([]);
  const [fileNames, setFileNames] = useState({
    excelFile: "",
  });
  const excelRef = useRef(null);

  useEffect(() => {
    const getData = async () => {
      const response = await apiClientProtected().get("/schools");
      console.log(response);
      setSchools(response.data.schools.data);
    };
    getData();
  }, []);

  const handleChange = async (e) => {
    if (e.target.name === "school_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/programs?school_id=${e.target.value}`
      );
      setPrograms(response.data.programs.data);
    } else if (e.target.name === "program_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });

      const response = await apiClientProtected().get(
        `/flows?program_id=${e.target.value}`
      );

      setFlows(response.data.learnYears.data);
    } else if (e.target.name === "flow_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    } else if (e.target.name === "excelFile") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    }
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    if (
      pageInfo.routeName !== "lecturers" ||
      pageInfo.routeName !== "administrations"
    ) {
      fd.append("school_id", fieldData.school_id);
      fd.append("program_id", fieldData.program_id);
      fd.append("flow_id", fieldData.flow_id);
    }

    fd.append("file", fieldData.excelFile);

    try {
      const response = await apiClientProtected().post(
        `/administration/${pageInfo.routeName}-import`,
        fd,
        {
          onUploadProgress: (progressEvent) => {
            const { loaded, total } = progressEvent;
            console.log("asdasd", loaded, total);
            let p = Math.floor((loaded * 100) / total);
            setPercent(p);
          },
        }
      );

      setUsersList(response.data.exist_users_not_imported);
      if (response.data.exist_users_not_imported.length) {
        setErrorMessage(response.data.message);
      }
      setSuccessMessage(`სულ აიტვირთა ${response.data.imported} მომხმარებელი`);
      const getResponse = await apiClientProtected().get(
        pageInfo.routeName === "bachelor"
          ? "/bachelor-registers"
          : pageInfo.routeName
      );
      if (pageInfo.routeName === "students") {
        setData(getResponse.data.students.data);
      } else if (pageInfo.routeName === "lecturers") {
        setData(getResponse.data.lecturers.data);
      } else if (pageInfo.routeName === "administrations") {
        setData(getResponse.data.administrations);
      } else {
        const data = getResponse.data.data.map((item) => {
          return {
            ...item.register_form_info,
            ...item,
          };
        });
        setData(data);
      }
      setIsSubmitting(false);
      setErrors(null);
      console.log(response);
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <FormElement onSubmit={handleSubmit}>
      <FormItem>
        {!["lecturers", "administrations"].includes(pageInfo.routeName) && (
          <>
            <FlexElement>
              <div>
                <label htmlFor="school_id">სკოლა</label>
                <select
                  name="school_id"
                  className="form-control mb-3 form-control form-control-solid "
                  id="school_id"
                  value={fieldData.school_id}
                  onChange={handleChange}
                >
                  <option key="123456789asdqwd" value="">
                    არჩევა
                  </option>
                  {schools.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name_ka}
                    </option>
                  ))}
                </select>
                {errors && (
                  <div className="text-danger">{errors.school_id}</div>
                )}
              </div>
              <div>
                <label htmlFor="program_id">პროგრამა</label>
                <select
                  name="program_id"
                  className="form-control mb-3 form-control form-control-solid "
                  id="program_id"
                  value={fieldData.program_id}
                  onChange={handleChange}
                >
                  <option key="123456789asdqwd" value="">
                    არჩევა
                  </option>
                  {programs.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name_ka}
                    </option>
                  ))}
                </select>
                {errors && (
                  <div className="text-danger">{errors.program_id}</div>
                )}
              </div>
            </FlexElement>
            <FlexElement>
              <div>
                <label htmlFor="flow_id">ნაკადი</label>
                <select
                  name="flow_id"
                  className="form-control mb-3 form-control form-control-solid "
                  id="flow_id"
                  value={fieldData.flow_id}
                  onChange={handleChange}
                >
                  <option key="123456789asdqwd" value="">
                    არჩევა
                  </option>
                  {flows.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name}
                    </option>
                  ))}
                </select>
                {errors && <div className="text-danger">{errors.flow_id}</div>}
              </div>
            </FlexElement>
          </>
        )}

        <FlexElement>
          <div className="form-group">
            <label htmlFor="excelFile">ატვირთე ფაილი</label>
            <input
              type="file"
              name="excelFile"
              ref={excelRef}
              style={{ display: "none" }}
              onChange={handleChange}
              accept=".xlsx, .xls, .csv"
            />
            <FileUpload onClick={() => triggerFile(excelRef)}>
              {fileNames["excelFile"] ? (
                <MdInsertDriveFile size={16} color="#009ef7" />
              ) : (
                <MdCloudUpload size={16} color="#555" />
              )}
              {fileNames["excelFile"] ? fileNames["excelFile"] : "ფაილის ატვ."}
            </FileUpload>
          </div>
        </FlexElement>

        <div
          style={{
            width: "100%",
            background: "rgb(167, 202, 237)",
            borderRadius: "4px",
            height: "4px",
          }}
        >
          <div
            style={{
              height: "100%",
              borderRadius: "4px",
              width: `${percent}%`,
              background: "#1976d2",
            }}
          ></div>
        </div>
        {errors && <div className="text-danger">{errors.file}</div>}
      </FormItem>
      <div>
        <button
          type="button"
          onClick={() =>
            window.open(
              `/assets/imports/${pageInfo.routeName}-import.xlsx`,
              "_blank"
            )
          }
        >
          <RiFileExcel2Fill color="#5EA162" size={22} />
        </button>{" "}
        <span>შაბლონის გადმოწერა</span>
      </div>
      <div className="d-flex align-items-center justify-content-center mt-4">
        <button
          className="btn btn-light-primary me-3"
          onClick={(e) => {
            e.preventDefault();
            setOpenModal(false);
          }}
        >
          დახურვა
        </button>

        {isSubmitting ? (
          <SubmitLoader type="primary" margin="mt-0" />
        ) : (
          <button
            className="btn btn-primary"
            type="submit"
            disabled={isSubmitting}
          >
            ატვირთვა
          </button>
        )}
      </div>
      {successMessage && <SuccessMessage>{successMessage}</SuccessMessage>}
      {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
      {usersList.length ? (
        <div className="font-bold mb-2">
          მომხმარებლები რომლებიც არ აიტვირთნენ
        </div>
      ) : null}
      <ul>
        {usersList.map((item) => (
          <UserItem key={item.id}>
            <span>
              {item.first_name} {item.last_name}
            </span>{" "}
            {item.email && -(<span>{item.email}</span>)}
          </UserItem>
        ))}
      </ul>
    </FormElement>
  );
};

export default ImportForm;

const FileUpload = styled.div`
  padding: 2rem 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  svg {
    color: #555;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;

const UserItem = styled.li`
  box-shadow: 0px 0px 3px 2px rgba(15, 119, 230, 0.13);
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
`;

const ErrorMessage = styled.div`
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  margin-top: 1rem;
  color: #58151c;
  background-color: #f8d7da;
  border: 1px solid #f1aeb5;
  border-radius: 0.375rem;
`;

const SuccessMessage = styled.div`
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;
  margin-top: 1rem;
  color: #0a3622;
  background-color: #d1e7dd;
  border: 1px solid #a3cfbb;
  border-radius: 0.375rem;
`;
