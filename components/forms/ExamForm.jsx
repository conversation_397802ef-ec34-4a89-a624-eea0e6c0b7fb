import { useState, useEffect } from "react";
import styled from "styled-components";
import apiClientProtected from "../../helpers/apiClient";
import DatePicker from "react-datepicker";
import { langs } from "./../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import SubmitLoader from "../ui/SubmitLoader";
import { MdClose } from "react-icons/md";
import { dateFormat } from "../../helpers/funcs";
import SweetAlert2 from "react-sweetalert2";

const ExamForm = ({ id }) => {
  const { errors, setErrors, setOpenModal, pageInfo, setData } =
    useTableContext();
  const { locale } = useLocaleContext();
  const [exams, setExams] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});

  useEffect(() => {
    const getData = async () => {
      try {
        const response = await apiClientProtected().get(
          `/syllabus/assessments/${id}`
        );
        setExams(response.data);
      } catch (err) {
        console.log(err);
      }
    };
    getData();
  }, []);

  const handleDate = (date, index, name) => {
    const data = [...exams];
    data[index][name] = date;
    data[index]["expiration_date"] = new Date(
      new Date(date).getTime() + 1000 * 60 * 60 * 24 * 10
    );
    setExams(data);
  };

  const removeExpDate = (index) => {
    const data = [...exams];
    data[index]["expiration_date"] = null;
    setExams(data);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    const fd = new FormData();
    for (let i = 0; i < exams.length; i++) {
      fd.append(
        `assignments[${i}][expiration_date]`,
        dateFormat(exams[i]["expiration_date"], "time", "-", "desc") + ":00"
      );
      fd.append(
        `assignments[${i}][exam_date]`,
        dateFormat(exams[i]["exam_date"], "time", "-", "desc") + ":00"
      );
      fd.append(`assignments[${i}][id]`, exams[i]["id"]);
    }

    try {
      const response = await apiClientProtected().post(
        `/syllabus/assignment/set-dates`,
        fd
      );

      setSuccess(true);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
    } catch (err) {
      setIsSubmitting(false);
    }
  };

  return (
    <Wrapper>
      <Table>
        <thead>
          <tr>
            <th>{locale && langs[locale]["title"]}</th>
            <th>{locale && langs[locale]["exam_date"]}</th>
            <th>{locale && langs[locale]["expiration_date"]}</th>
          </tr>
        </thead>
        <tbody>
          {exams.map((item, index) => (
            <tr key={item.id}>
              <td>{item.assessment_component.name_ka}</td>
              <td>
                <DatePicker
                  calendarStartDay={1}
                  className="form-control form-control-solid example-custom-input"
                  selected={item.exam_date ? new Date(item.exam_date) : null}
                  placeholderText="Select a date"
                  showTimeSelect
                  onChange={(date) => handleDate(date, index, "exam_date")}
                  dateFormat="dd/MM/yyyy - h:mm aa"
                />
              </td>
              <td>
                {item.expiration_date && (
                  <span
                    className="remove-date"
                    onClick={() => removeExpDate(index)}
                  >
                    <MdClose />
                  </span>
                )}
                <DatePicker
                  calendarStartDay={1}
                  className="form-control form-control-solid example-custom-input"
                  selected={
                    item.expiration_date ? new Date(item.expiration_date) : null
                  }
                  placeholderText="Select a date"
                  onChange={(date) =>
                    handleDate(date, index, "expiration_date")
                  }
                  showTimeSelect
                  dateFormat="dd/MM/yyyy - h:mm aa"
                />
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
      <Controller>
        <button
          className="btn btn-light-primary me-3"
          onClick={() => setOpenModal(false)}
        >
          {locale && langs[locale]["close"]}
        </button>
        {isSubmitting ? (
          <SubmitLoader type="primary" margin="mt-0" />
        ) : (
          <button
            className="btn btn-primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {locale && langs[locale]["save"]}
          </button>
        )}
      </Controller>

      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </Wrapper>
  );
};

export default ExamForm;

const Wrapper = styled.div`
  width: 100%;
`;

const Table = styled.table`
  width: 100%;
  border: 1px solid #eeeeef;
  border-collapse: collapse;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  td,
  th {
    padding: 0.75rem;
    border: 1px solid #eeeeef;
  }
  td {
    position: relative;
  }
  .remove-date {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    position: absolute;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    right: 6px;
    background-color: #fff;
    top: 4px;
  }
`;

const Controller = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
`;
