import { useEffect, useState, useRef } from 'react';
import apiClientProtected from '../../../helpers/apiClient';
import { FormElement, FormItem, FlexElement, FileUpload } from './../styles'
import { MdCloudUpload, MdInsertDriveFile } from 'react-icons/md'

import { useTableContext } from '../../context/TableContext';
import SubmitLoader from './../../ui/SubmitLoader'

import SweetAlert2 from 'react-sweetalert2';


function AdminCreationForm({ fetchLink }) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [imageSrc, setImageSrc] = useState('')
  const [cv, setCv] = useState('')
  const [preview, setPreview] = useState('')
  const [fileName, setFileName] = useState('')
  const [swalProps, setSwalProps] = useState({});
  const [userAdded, setUserAdded] = useState(false)
  const [fields, setFields] = useState([])
  const [selectField, setSelectField] = useState('1')
  
  const { handleDataSubmit, relationFields, setOpenModal, errors, setErrors, pageInfo } = useTableContext()

  const [fieldData, setFieldData] = useState({
    first_name: "",
    last_name: "",
    identity_number: "",
    phone:"",
    email: "",
    school_id: '',
    administration_item_id: "",
    administration_position_id: '',
    photo: "",
    cv: ''
  })

  const imageRef = useRef(null)
  const cvRef = useRef(null)

  useEffect(() => {
    if(imageSrc) {
        const reader = new FileReader()
        reader.onloadend = () => {
            setPreview(reader.result)
        }
        reader.readAsDataURL(imageSrc)
    }
  }, [imageSrc])

  // const { relationFields } = useTableContext()
  const handleChange = (e) => {
    if(e.target.name === 'photo') {
      setImageSrc(e.target.files[0])
      setFieldData({...fieldData, [e.target.name]: e.target.files[0]})
    } else if(e.target.name === 'cv') {
      setCv(e.target.files[0])
      setFieldData({...fieldData, [e.target.name]: e.target.files[0]})
    } else {
      setFieldData({...fieldData, [e.target.name]: e.target.value})
    }
  }
  
  console.log(relationFields)
  const triggerFile = (name) => {
    name.current.click()
  }


  const handleSubmit = async (e) => {
      e.preventDefault()
      const fd = new FormData()

      for(let key in fieldData) {
        fd.append(key, fieldData[key])
      }

      try {
          const response = await apiClientProtected().post(fetchLink, fd)
          pageInfo.routeName === 'library-subject' 
              ? handleDataSubmit(response.data.data)
              : handleDataSubmit(response.data)
          setOpenModal(false)
          setErrors(null)
          setIsSubmitting(false)
          setUserAdded(true)
          setSwalProps({
              show: true,
              title: 'დამატებულია!',
              text: 'წარმატებით დაემატა',
              icon: 'success',
              confirmButtonColor: "#009ef7"
          });

      } catch(err) {
          setIsSubmitting(false)
          setErrors(err.response.data)
          console.log(err.response)
      }
  }

    return (
      <>
          <FormElement onSubmit={handleSubmit}>
            <FormItem>
              <h6>პერსონალური ინფორმაცია</h6>
              <FlexElement>
                <div>
                  <label htmlFor="first_name">სახელი</label>
                  <input 
                    type="text" 
                    name="first_name" 
                    className="form-control mb-3 form-control form-control-solid " 
                    placeholder="სახელი" 
                    id="first_name" 
                    value={fieldData.first_name}
                    onChange={handleChange} />
                    {errors && <div className="text-danger">{errors.first_name}</div>}
                </div>
                
                <div>
                  <label htmlFor="last_name">გვარი</label>
                  <input 
                    type="text" 
                    name="last_name" 
                    className="form-control mb-3 form-control form-control-solid " 
                    placeholder="გვარი" 
                    id="last_name" 
                    value={fieldData.last_name} 
                    onChange={handleChange} />
                    {errors && <div className="text-danger">{errors.last_name}</div>}
                </div>
              </FlexElement>

              <FlexElement>
                <div>
                  <label htmlFor="identity_number">პირადი ნომერი</label>
                    <input 
                      type="text" 
                      name="identity_number" 
                      className="form-control mb-3 form-control form-control-solid " 
                      placeholder="პირადი ნომერი" 
                      id="identity_number" 
                      value={fieldData.identity_number}
                      onChange={handleChange} />
                      {errors && <div className="text-danger">{errors.identity_number}</div>}
                </div>
              </FlexElement>
            </FormItem>
            <FormItem>

            
              <h6>საკონტაქტო ინფორმაცია</h6>
              <FlexElement>
                <div>
                  <label htmlFor="phone">ტელეფონი</label>
                  <input 
                    type="text" 
                    name="phone" 
                    className="form-control mb-3 form-control form-control-solid " 
                    placeholder="ტელეფონი" 
                    id="phone" 
                    value={fieldData.phone}
                    onChange={handleChange} />
                    {errors && <div className="text-danger">{errors.phone}</div>}
                </div>
              
                <div>
                  <label htmlFor="email">ელ-ფოსტა</label>
                  <input 
                    type="text" 
                    name="email" 
                    className="form-control mb-3 form-control form-control-solid " 
                    placeholder="ელ-ფოსტა" 
                    id="email" 
                    value={fieldData.email} 
                    onChange={handleChange} />
                    {errors && <div className="text-danger">{errors.email}</div>}
                </div>
              </FlexElement>
              </FormItem>

              <FormItem>
                <h6>სხვადასხვა</h6>
                <FlexElement>
                  <div className="form-group">
                    <label htmlFor="administration_position_id">ადმინისტრაციული პოზიცია</label>
                    <select
                      className="form-control mb-3 form-control form-control-solid " 
                      name="administration_position_id" 
                      id="administration_position_id" 
                      value={fieldData.administration_position_id}
                      onChange={handleChange}>
                        <option value="" key="13246798asd">არჩევა</option>
                        {relationFields['positions'] && Object.keys(relationFields['positions'].options).map((field, index) => (
                            <option value={field} key={index}>{relationFields['positions'].options[field]}</option>
                        ))}
                      </select>
                      {errors && <div className="text-danger">{errors.administration_position_id}</div>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="selectField">ერთეულის არჩევა</label>
                    <select
                      className="form-control mb-3 form-control form-control-solid " 
                      name="selectField" 
                      id="selectField" 
                      value={selectField}
                      onChange={(e) => setSelectField(e.target.value)}>
                        <option value="" key="13246798asd">არჩევა</option>
                        <option value="1" key="1">სკოლა</option>
                        <option value="2" key="2">ადმინისტრაციული ერთეული</option>
                      </select>
                  </div>
                </FlexElement>

                <FlexElement>
                  { selectField === '1' ? <div className="form-group">
                    <label htmlFor="school_id">სკოლა</label>
                    <select
                      className="form-control mb-3 form-control form-control-solid " 
                      name="school_id" 
                      id="school_id" 
                      value={fieldData.school_id}
                      onChange={handleChange}>
                        <option value="" key="13246798asd">არჩევა</option>
                        {relationFields['school'] && Object.keys(relationFields['school'].options).map((field, index) => (
                            <option value={field} key={index}>{relationFields['school'].options[field]}</option>
                        ))}
                      </select>
                      {errors && <div className="text-danger">{errors.school_id}</div>}
                  </div>
                  :
                  <div className="form-group">
                    <label htmlFor="administration_item_id">ადმინისტრაციული ერთეული</label>
                    <select
                      className="form-control mb-3 form-control form-control-solid " 
                      name="administration_item_id" 
                      id="administration_item_id" 
                      value={fieldData.administration_item_id}
                      onChange={handleChange}>
                        <option value="" key="13246798asd">არჩევა</option>
                        {relationFields['items'] && Object.keys(relationFields['items'].options).map((field, index) => (
                            <option value={field} key={index}>{relationFields['items'].options[field]}</option>
                        ))}
                      </select>
                      {errors && <div className="text-danger">{errors.administration_item_id}</div>}
                  </div>}
                </FlexElement>
              </FormItem>
              
             

              <FormItem>
                <h6>მიმაგრებული ფაილი</h6>
                
                <FlexElement>
                  <div className="form-group">
                    <label htmlFor="administration_item_id">ფოტო</label>
                    <input 
                      type="file" 
                      name="photo"
                      ref={imageRef}
                      style={{display: 'none'}}
                      onChange={handleChange}
                      accept="image/png, image/jpg, image/jpeg" />
                    <FileUpload onClick={() => triggerFile(imageRef)} style={{backgroundImage: `url(${preview})`}}>
                      <MdCloudUpload size={16} />
                      Upload File
                    </FileUpload>
                    {/* <div>
                      <img src={preview} alt="" />
                    </div> */}
                    {errors && <div className="text-danger">{errors.administration_item_id}</div>}
                  </div>
                  <div className="form-group">
                    <label htmlFor="administration_item_id">cv</label>
                    <FileUpload onClick={() => triggerFile(cvRef)}>
                      {cv ? <MdInsertDriveFile size={16} /> : <MdCloudUpload size={16} />}
                      {cv ? `${cv.name}` : 'ფაილის ატვირთვა'}
                    </FileUpload>
                    <input 
                      type="file" 
                      name="cv"
                      ref={cvRef}
                      style={{display: 'none'}}
                      onChange={handleChange}
                      accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword" />
                    
                    {errors && <div className="text-danger">{errors.administration_item_id}</div>}
                  </div>
                </FlexElement>
              </FormItem>

              
                  

              <div className='d-flex align-items-center justify-content-center mt-4'>
                  <button
                      className='btn btn-light-primary me-3'
                      onClick={(e) => {
                          e.preventDefault()
                          setOpenModal(false)
                      }}
                  >დახურვა</button>

                  {
                      isSubmitting
                          ?
                          <SubmitLoader type="primary" margin="mt-0" />
                          :
                          <button className='btn btn-primary' type='submit' disabled={isSubmitting}>
                              დამატება
                          </button>
                  }
              </div>
          </FormElement>

          {
              userAdded && <SweetAlert2
                  {...swalProps}
                  onConfirm={() => setOpenModal(false)}
              />
          }
      </>
    )
}

export default AdminCreationForm

const FormElement = styled.form`
  max-width: 800px;
  margin-top: 2rem;
`

const FormItem = styled.div`
  margin-bottom: 1.75rem;
  h6 {
    margin-bottom: .75rem;
  }
`
const FlexElement = styled.div`
  display: flex;
  gap: 1rem;
  div {
    flex: 1;
  }
  label {
    margin-bottom: .5rem;
  }
`

const CvLogo = styled.div`
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  img {
    width: 100%;
  }
`;

const FileUpload = styled.div`
  padding: 2rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: .5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  svg {
    color: #555;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`