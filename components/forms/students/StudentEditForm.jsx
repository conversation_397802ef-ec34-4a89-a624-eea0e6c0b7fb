import { useEffect, useState, useRef } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import { useTableContext } from "../../context/TableContext";
import DatePicker from "react-datepicker";
import { dateFormat } from "../../../helpers/funcs";
import { Editor } from "@tinymce/tinymce-react";
import { FormElement, FormItem, FlexElement, FileUpload } from "./../styles";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import { MdCloudUpload } from "react-icons/md";

import SweetAlert2 from "react-sweetalert2";

function StudentEditForm({ fetchLink, fields, setOpenModal, id, data }) {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageSrc, setImageSrc] = useState("");
  const [fileNames, setFileNames] = useState({
    cv_file_name: "",
    diploma_file_name: "",
    motivation_article_file_name: "",
    transcript_file_name: "",
  });
  const [swalProps, setSwalProps] = useState({});
  const [schools, setSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [learnYears, setLearnYears] = useState([]);
  const [groups, setGroups] = useState([]);
  const [userAdded, setUserAdded] = useState(false);
  const [preview, setPreview] = useState("");
  const imageRef = useRef(null);
  const cv_file_name = useRef(null);
  const diploma_file_name = useRef(null);
  const motivation_article_file_name = useRef(null);
  const transcript_file_name = useRef(null);

  const KEYS = [
    "photo",
    "cv_file_name",
    "transcript_file_name",
    "diploma_file_name",
    "motivation_article_file_name",
  ];

  const editorRef = useRef(null);

  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();

  const [fieldData, setFieldData] = useState({
    name: "",
    surname: "",
    personal_id: "",
    personal_id_number: "",
    sex: "",
    birthday: "",
    citizenship: "",
    address: "",
    phone: "",
    email: "",
    status_id: "",
    school_id: "",
    program_id: "",
    group_id: "",
    learn_year_id: "",
    mobility: "0",
    diploma_taken: "0",
    diploma_taken_date: "",
    diploma_number: "",
    enrollment_date: "",
    photo: "",
    cv_file_name: "",
    motivation_article_file_name: "",
    transcript_file_name: "",
    diploma_file_name: "",
    enrollment_order: "",
    basics_of_enrollement_id: "",
    notes: "",
    bio: "",
  });

  useEffect(() => {
    const birthday =
      data.birthday && new Date(data.birthday.split("-").reverse().join("-"));
    const diploma_taken_date =
      data.diploma_taken_date &&
      new Date(data.diploma_taken_date.split("-").reverse().join("-"));
    const enrollment_date =
      data.enrollment_date &&
      new Date(data.enrollment_date.split("-").reverse().join("-"));
    setFieldData({ ...data, birthday, diploma_taken_date, enrollment_date });
    setFileNames({
      ...fileNames,
      cv_file_name: data.cv_file_name,
      diploma_file_name: data.diploma_file_name,
      motivation_article_file_name: data.motivation_article_file_name,
      transcript_file_name: data.transcript_file_name,
    });
  }, []);

  useEffect(() => {
    const getSchools = async () => {
      try {
        const response = await apiClientProtected().get("/schools");
        setSchools(response.data.schools.data);
        const programResponse = await apiClientProtected().get(
          `/student/filtered-data?school_id=${fieldData.school_id}`
        );
        console.log(programResponse);
        setPrograms(programResponse.data.program_id);
        const groupsResponse = await apiClientProtected().get(
          `/student/filtered-data?program_id=${fieldData.program_id}&school_id=${fieldData.school_id}`
        );
        setGroups(groupsResponse.data.group_id);
        setLearnYears(groupsResponse.data.learn_year_id);
      } catch (err) {
        console.log(err);
      }
    };
    // setFieldData({ ...data })

    getSchools();
  }, []);

  useEffect(() => {
    if (imageSrc) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(imageSrc);
    }
  }, [imageSrc]);

  const handleChange = async (e) => {
    if (e.target.name === "school_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/student/filtered-data?school_id=${e.target.value}`
      );
      console.log(response);
      setPrograms(response.data.program_id);
    } else if (e.target.name === "program_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/student/filtered-data?program_id=${e.target.value}&school_id=${fieldData.school_id}`
      );
      setGroups(response.data.group_id);
      setLearnYears(response.data.learn_year_id);
      // console.log(response)
    } else if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else if (e.target.name === "photo") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setImageSrc(e.target.files[0]);
    } else if (e.target.type === "file") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
      setFileNames({ ...fileNames, [e.target.name]: e.target.files[0].name });
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleEditorChange = (e, name) => {
    setFieldData({ ...fieldData, [name]: e.target.getContent() });
    console.log(name, e.target.getContent());
  };

  const triggerFile = (name) => {
    name.current.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    console.log(KEYS.includes("photo"), typeof fieldData["photo"]);

    fd.append("_method", "PUT");

    for (const key in fieldData) {
      // console.log(key);
      // Check if key contains value append to FormData object
      if (fieldData[key]) {
        if (
          key === "enrollment_date" ||
          key === "birthday" ||
          key === "diploma_taken_date"
        ) {
          fd.append(key, dateFormat(fieldData[key], null, "/"));
        } else if (KEYS.includes(key) && typeof fieldData[key] !== "string") {
          console.log(key, fieldData[key], typeof key);
          fd.append(key, fieldData[key]);
        } else if (!KEYS.includes(key)) {
          console.log(key, fieldData[key], typeof key);
          fd.append(key, fieldData[key]);
        }
      }
    }

    fd.append("sex", fieldData.sex);

    try {
      const response = await apiClientProtected().post(
        `${fetchLink}/${id}`,
        fd
      );

      handleDataEdit(response.data);
      setIsSubmitting(false);
      setErrors(null);
      setImageSrc("");
      setFileNames([]);
      setUserAdded(true);
      setOpenModal(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data);
      if (err) {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <FormItem>
          <h6>{locale && langs[locale]["personal_info"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="name">
                {locale && langs[locale]["first_name"]}
              </label>
              <input
                type="text"
                name="name"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["first_name"]}
                id="name"
                value={fieldData.name}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.name}</div>}
            </div>

            <div>
              <label htmlFor="surname">
                {locale && langs[locale]["last_name"]}
              </label>
              <input
                type="text"
                name="surname"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["last_name"]}
                id="surname"
                value={fieldData.surname}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.surname}</div>}
            </div>
          </FlexElement>

          <FlexElement>
            <div>
              <label htmlFor="personal_id">
                {locale && langs[locale]["id_number"]}
              </label>
              <input
                type="text"
                name="personal_id"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["id_number"]}
                id="personal_id"
                value={fieldData.personal_id}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.personal_id}</div>
              )}
            </div>

            <div>
              <label htmlFor="personal_id_number">
                {locale && langs[locale]["id_card_number"]}
              </label>
              <input
                type="text"
                name="personal_id_number"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["id_card_number"]}
                id="personal_id_number"
                value={fieldData.personal_id_number}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.personal_id_number}</div>
              )}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="sex">{locale && langs[locale]["gender"]}</label>
              <select
                name="sex"
                className="form-control mb-3 form-control form-control-solid "
                id="sex"
                value={fieldData.sex}
                onChange={handleChange}
              >
                <option key="12346587asdqwe" value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                <option key="1" value="1">
                  {locale && langs[locale]["male"]}
                </option>
                <option key="2" value="0">
                  {locale && langs[locale]["female"]}
                </option>
              </select>
              {errors && <div className="text-danger">{errors.sex}</div>}
            </div>

            <div>
              <label htmlFor="birthday">
                {locale && langs[locale]["date_of_birth"]}
              </label>
              <DatePicker
                className="form-control mb-3 form-control form-control-solid"
                dateFormat="dd/MM/yyyy"
                placeholderText="dd-mm-yyyy"
                showYearDropdown
                scrollableYearDropdown
                yearDropdownItemNumber={40}
                selected={fieldData.birthday}
                onChange={(date) => handleDate(date, "birthday")}
              />
              {errors && <div className="text-danger">{errors.birthday}</div>}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="citizenship">
                {locale && langs[locale]["citizenship"]}
              </label>
              <input
                type="text"
                name="citizenship"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["citizenship"]}
                id="citizenship"
                value={fieldData.citizenship}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.citizenship}</div>
              )}
            </div>

            <div>
              <label htmlFor="address">
                {locale && langs[locale]["address"]}
              </label>
              <input
                type="text"
                name="address"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["address"]}
                id="address"
                value={fieldData.address}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.address}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["contact_info"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="phone">{locale && langs[locale]["phone"]}</label>
              <input
                type="text"
                name="phone"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["phone"]}
                id="phone"
                value={fieldData.phone}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.phone}</div>}
            </div>

            <div>
              <label htmlFor="email">{locale && langs[locale]["email"]}</label>
              <input
                type="text"
                name="email"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["email"]}
                id="email"
                value={fieldData.email}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.email}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["program"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="sex">{locale && langs[locale]["status"]}</label>
              <select
                name="status_id"
                className="form-control mb-3 form-control form-control-solid "
                id="status_id"
                value={fieldData.status_id}
                onChange={handleChange}
              >
                <option key="12346587asdqwe" value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {relationFields &&
                  relationFields["status"]?.options &&
                  Object.keys(relationFields["status"]?.options).map(
                    (field) => (
                      <option key={field} value={field}>
                        {relationFields["status"].options[field]}
                      </option>
                    )
                  )}
              </select>
              {errors && <div className="text-danger">{errors.status_id}</div>}
            </div>

            <div>
              <label htmlFor="school_id">
                {locale && langs[locale]["school"]}
              </label>
              <select
                name="school_id"
                className="form-control mb-3 form-control form-control-solid "
                id="school_id"
                value={fieldData.school_id}
                onChange={handleChange}
              >
                <option key="12346587asdqwe" value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {schools?.map((item, index) => (
                  <option key={index} value={item.id}>
                    {item.name_ka}
                  </option>
                ))}
              </select>
              {errors && <div className="text-danger">{errors.school_id}</div>}
            </div>
          </FlexElement>

          <FlexElement>
            <div>
              <label htmlFor="program_id">
                {locale && langs[locale]["program"]}
              </label>
              <select
                name="program_id"
                className="form-control mb-3 form-control form-control-solid "
                id="program_id"
                value={fieldData.program_id}
                onChange={handleChange}
              >
                <option key="12346587asdqwe" value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {Object.entries(programs)?.map((item, index) => (
                  <option key={index} value={item[0]}>
                    {item[1]}
                  </option>
                ))}
              </select>
              {errors && <div className="text-danger">{errors.program_id}</div>}
            </div>

            <div>
              <label htmlFor="group_id">
                {locale && langs[locale]["groups"]}
              </label>
              <select
                name="group_id"
                className="form-control mb-3 form-control form-control-solid "
                id="group_id"
                value={fieldData.group_id}
                onChange={handleChange}
              >
                <option key="12346587asdqwe" value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {Object.entries(groups)?.map((item, index) => (
                  <option key={index} value={item[0]}>
                    {item[1]}
                  </option>
                ))}
              </select>
              {errors && <div className="text-danger">{errors.group_id}</div>}
            </div>
            <div>
              <label htmlFor="learn_year_id">
                {locale && langs[locale]["flows"]}
              </label>
              <select
                name="learn_year_id"
                className="form-control mb-3 form-control form-control-solid "
                id="learn_year_id"
                value={fieldData.learn_year_id}
                onChange={handleChange}
              >
                <option key="12346587asdqwe" value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {Object.entries(learnYears)?.map((item, index) => (
                  <option key={index} value={item[0]}>
                    {item[1]}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">{errors.learn_year_id}</div>
              )}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="enrollment_date">
                {locale && langs[locale]["enrollment_date"]}
              </label>
              <DatePicker
                className="form-control mb-3 form-control form-control-solid"
                dateFormat="dd/MM/yyyy"
                placeholderText="dd-mm-yyyy"
                showYearDropdown
                scrollableYearDropdown
                yearDropdownItemNumber={10}
                selected={fieldData.enrollment_date}
                onChange={(date) => handleDate(date, "enrollment_date")}
              />
              {errors && (
                <div className="text-danger">{errors.enrollment_date}</div>
              )}
            </div>

            <div>
              <label htmlFor="enrollment_order">
                {locale && langs[locale]["order_number"]}
              </label>
              <input
                type="text"
                name="enrollment_order"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["order_number"]}
                id="enrollment_order"
                value={fieldData.enrollment_order}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.enrollment_order}</div>
              )}
            </div>
            <div>
              <label htmlFor="basics_of_enrollement_id">
                {locale && langs[locale]["enrollment_basis"]}
              </label>
              <select
                name="basics_of_enrollement_id"
                className="form-control mb-3 form-control form-control-solid "
                id="basics_of_enrollement_id"
                value={fieldData.basics_of_enrollement_id}
                onChange={handleChange}
              >
                <option value="">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {relationFields &&
                  relationFields["basicOfEnrollments"]?.options &&
                  Object.keys(
                    relationFields["basicOfEnrollments"]?.options
                  ).map((field) => (
                    <option key={field} value={field}>
                      {relationFields["basicOfEnrollments"].options[field]}
                    </option>
                  ))}
              </select>
              {errors && (
                <div className="text-danger">
                  {errors.basics_of_enrollement_id}
                </div>
              )}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="mobility" style={{ display: "block" }}>
                {locale && langs[locale]["mobility"]}
              </label>
              <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="mobility"
                  name="mobility"
                  value={fieldData.mobility}
                  checked={
                    fieldData.mobility === 1 || fieldData.mobility === "1"
                  }
                  onChange={handleChange}
                />
                <span className="form-check-label fw-bold text-muted">
                  {fieldData.mobility === "0"
                    ? locale && langs[locale]["no"]
                    : locale && langs[locale]["yes"]}
                </span>
              </div>
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["others"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="diploma_taken" style={{ display: "block" }}>
                {locale && langs[locale]["diploma_issued"]}
              </label>
              <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="diploma_taken"
                  name="diploma_taken"
                  value={fieldData.diploma_taken}
                  checked={
                    fieldData.diploma_taken === 1 ||
                    fieldData.diploma_taken === "1"
                  }
                  onChange={handleChange}
                />
                <span className="form-check-label fw-bold text-muted">
                  {fieldData.diploma_taken === "0"
                    ? locale && langs[locale]["no"]
                    : locale && langs[locale]["yes"]}
                </span>
              </div>
            </div>

            {(fieldData.diploma_taken === "1" ||
              fieldData.diploma_taken === 1) && (
              <>
                <div>
                  <label htmlFor="diploma_taken_date">
                    {locale && langs[locale]["diploma_issue_date"]}
                  </label>
                  <DatePicker
                    className="form-control mb-3 form-control form-control-solid"
                    dateFormat="dd/MM/yyyy"
                    placeholderText="dd-mm-yyyy"
                    showYearDropdown
                    scrollableYearDropdown
                    yearDropdownItemNumber={10}
                    selected={fieldData.diploma_taken_date}
                    onChange={(date) => handleDate(date, "diploma_taken_date")}
                  />
                  {errors && (
                    <div className="text-danger">{errors.diploma_taken_date}</div>
                  )}
                </div>
                <div>
                  <label htmlFor="diploma_number">
                    {locale && langs[locale]["diploma_number"]}
                  </label>
                  <input
                    type="text"
                    name="diploma_number"
                    className="form-control mb-3 form-control form-control-solid"
                    placeholder={locale && langs[locale]["diploma_number"]}
                    id="diploma_number"
                    value={fieldData.diploma_number}
                    onChange={handleChange}
                  />
                  {errors && (
                    <div className="text-danger">{errors.diploma_number}</div>
                  )}
                </div>
              </>
            )}
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="">{locale && langs[locale]["description"]}</label>
              <Editor
                tinymceScriptSrc={
                  process.env.PUBLIC_URL + "/tinymce/tinymce.min.js"
                }
                // onInit={(evt, editor) => {
                //     editorRef.current = editor
                //     setValue(name, editorRef.current?.getContent())
                // }}
                initialValue={fieldData.notes}
                onChange={(e) => handleEditorChange(e, "notes")}
                init={{
                  height: 200,
                  menubar: false,
                  branding: false,
                  plugins: [
                    "advlist",
                    "autolink",
                    "lists",
                    "link",
                    "image",
                    "charmap",
                    "anchor",
                    "searchreplace",
                    "visualblocks",
                    "code",
                    "fullscreen",
                    "insertdatetime",
                    "media",
                    "table",
                    "preview",
                    "help",
                    "wordcount",
                  ],
                  toolbar:
                    "undo redo | blocks | " +
                    "bold italic forecolor | alignleft aligncenter " +
                    "alignright alignjustify | bullist numlist outdent indent | " +
                    "removeformat | help",
                  content_style:
                    "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
                }}
              />
            </div>
            <div>
              <label htmlFor="">{locale && langs[locale]["bio"]}</label>
              <Editor
                tinymceScriptSrc={
                  process.env.PUBLIC_URL + "/tinymce/tinymce.min.js"
                }
                // onInit={(evt, editor) => {
                //     editorRef.current = editor
                //     setValue(name, editorRef.current?.getContent())
                // }}
                initialValue={fieldData.bio}
                onChange={(e) => handleEditorChange(e, "bio")}
                init={{
                  height: 200,
                  menubar: false,
                  branding: false,
                  plugins: [
                    "advlist",
                    "autolink",
                    "lists",
                    "link",
                    "image",
                    "charmap",
                    "anchor",
                    "searchreplace",
                    "visualblocks",
                    "code",
                    "fullscreen",
                    "insertdatetime",
                    "media",
                    "table",
                    "preview",
                    "help",
                    "wordcount",
                  ],
                  toolbar:
                    "undo redo | blocks | " +
                    "bold italic forecolor | alignleft aligncenter " +
                    "alignright alignjustify | bullist numlist outdent indent | " +
                    "removeformat | help",
                  content_style:
                    "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
                }}
              />
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["attached_files"]}</h6>
          <FlexElement>
            <div className="form-group">
              <label htmlFor="photo">{locale && langs[locale]["photo"]}</label>
              <input
                type="file"
                name="photo"
                ref={imageRef}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="image/png, image/jpg, image/jpeg"
              />
              <FileUpload
                onClick={() => triggerFile(imageRef)}
                style={{
                  backgroundImage: !preview
                    ? `url(${process.env.NEXT_PUBLIC_STORAGE}${fieldData.photo})`
                    : `url(${preview})`,
                }}
              >
                <MdCloudUpload size={16} color="#555" />
                {locale && langs[locale]["file_upload_short"]}
              </FileUpload>
              {errors && <div className="text-danger">{errors.photo}</div>}
            </div>
            <div className="form-group">
              <label htmlFor="cv_file_name">cv</label>
              <input
                type="file"
                name="cv_file_name"
                ref={cv_file_name}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
              />
              <FileUpload onClick={() => triggerFile(cv_file_name)}>
                <MdCloudUpload size={16} color="#555" />
                {fileNames["cv_file_name"] ? (
                  <span>{fileNames["cv_file_name"]}</span>
                ) : (
                  locale && langs[locale]["file_upload_short"]
                )}
              </FileUpload>
              {errors && (
                <div className="text-danger">{errors.cv_file_name}</div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="diploma_file_name">
                {locale && langs[locale]["diploma_copy"]}
              </label>
              <input
                type="file"
                name="diploma_file_name"
                ref={diploma_file_name}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
              />
              <FileUpload onClick={() => triggerFile(diploma_file_name)}>
                <MdCloudUpload size={16} color="#555" />
                {fileNames["diploma_file_name"] ? (
                  <span>{fileNames["diploma_file_name"]}</span>
                ) : (
                  locale && langs[locale]["file_upload_short"]
                )}
              </FileUpload>
              {errors && (
                <div className="text-danger">{errors.diploma_file_name}</div>
              )}
            </div>
          </FlexElement>
          <div style={{ margin: "1rem 0 0" }}></div>
          <FlexElement>
            <div className="form-group">
              <label htmlFor="administration_item_id">დიპლომის დანართი</label>
              <input
                type="file"
                name="motivation_article_file_name"
                ref={motivation_article_file_name}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
              />
              <FileUpload
                onClick={() => triggerFile(motivation_article_file_name)}
              >
                <MdCloudUpload size={16} color="#555" />
                {fileNames["motivation_article_file_name"] ? (
                  <span>{fileNames["motivation_article_file_name"]}</span>
                ) : (
                  locale && langs[locale]["file_upload_short"]
                )}
              </FileUpload>
              {errors && (
                <div className="text-danger">
                  {errors.motivation_article_file_name}
                </div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="transcript_file_name">
                {locale && langs[locale]["motivation_letter"]}
              </label>
              <input
                type="file"
                name="transcript_file_name"
                ref={transcript_file_name}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
              />
              <FileUpload onClick={() => triggerFile(transcript_file_name)}>
                <MdCloudUpload size={16} color="#555" />
                {fileNames["transcript_file_name"] ? (
                  <span>{fileNames["transcript_file_name"]}</span>
                ) : (
                  locale && langs[locale]["file_upload_short"]
                )}
              </FileUpload>
              {errors && (
                <div className="text-danger">{errors.transcript_file_name}</div>
              )}
            </div>
          </FlexElement>
        </FormItem>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </div>
      </FormElement>

      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default StudentEditForm;
