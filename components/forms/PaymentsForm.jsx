import { useState, useEffect } from "react";
import styled from "styled-components";
import { MdClose } from "react-icons/md";
import { HiPlusSm } from "react-icons/hi";
import DatePicker from "react-datepicker";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import ButtonLoader from "./../ui/ButtonLoader";
import apiClientProtected from "../../helpers/apiClient";
import { dateFormat } from "../../helpers/funcs";
import { useTableContext } from "./../context/TableContext";
import SweetAlert2 from "react-sweetalert2";
import "react-datepicker/dist/react-datepicker.css";

const PaymentsForm = ({
  user_id,
  edoc_template_id,
  mainColor,
  setShowModal,
  setIndSchedule,
}) => {
  const { locale } = useLocaleContext();
  const { pageInfo, errors, setErrors } = useTableContext();
  const { setOpenModal } = useTableContext();

  const threeMonths = 3 * 24 * 60 * 60 * 1000;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorText, setErrorText] = useState("");
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [salary, setSalary] = useState(0);
  const [staticSalary, setStaticSalary] = useState(0);
  const [activeClass, setActiveClass] = useState(1);
  const [learnYearManual, setLearnYearManual] = useState("");
  const [learnYearAuto, setLearnYearAuto] = useState("");
  const [initTotalAmount, setInitTotalAmount] = useState(0);
  const [isManual, setIsManual] = useState(true);
  const [payments, setPayments] = useState([
    { id: 1, start_date: "", end_date: "", amount: "" },
    { id: 2, start_date: "", end_date: "", amount: "" },
  ]);

  useEffect(() => {
    const getResponse = async () => {
      try {
        const response = await apiClientProtected().get(
          `/finances/calendar?user_id=${user_id}`
        );
        console.log(response, pageInfo, "Mikis Theodorakis");
        setSalary(Number(response.data.amount));
        setStaticSalary(Number(response.data.amount));
        setInitTotalAmount(Number(response.data.amount));
        setLearnYearAuto(response.data.finance_learn_year_auto);
        setLearnYearManual(response.data.finance_learn_year_manual);
        if (response.data.data.calendars.length) {
          const calendar = response.data.data.calendars.map((item) => ({
            id: item.id,
            start_date: new Date(),
            end_date: new Date(item.end_date.split("T")[0]),
            amount: item.amount,
          }));
          console.log(calendar);
          setPayments(calendar);
        } else {
          setPayments([
            { id: 1, start_date: "", amount: "" },
            { id: 2, start_date: "", amount: "" },
          ]);
        }
        console.log(learnYearAuto);
      } catch (err) {
        console.log(err);
      }
    };

    getResponse();
  }, [user_id]);

  useEffect(() => {
    setSalary(initTotalAmount);
  }, [activeClass]);

  useEffect(() => {
    if (isManual) {
      const totalAmount = salary;
      const total = payments.reduce(
        (total, item) => total + Number(item.amount),
        0
      );
      setSalary(initTotalAmount - total);
      console.log(total);
    }
  }, [payments]);

  const handleChange = (e, index) => {
    const copied = [...payments];
    const newArray = copied.map((item, i) => {
      if (index === i) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });

    setPayments(newArray);
  };

  const handleDate = (date, name, index) => {
    console.log(name, date, index);
    const copied = [...payments];

    const newArray = copied.map((item, i) => {
      if (Number(index) === Number(i)) {
        const interval = new Date(date).getTime() + threeMonths;
        item[name] = date;
        item["end_date"] = new Date(interval);
      }
      return item;
    });

    setPayments(newArray);
  };

  const handleAddPayment = () => {
    if (payments.length === 10) {
      return;
    }
    const copied = [...payments];
    copied.push({
      id: Math.random().toString(),
      start_date: "",
      end_date: "",
      amount: "",
    });

    setPayments(copied);
  };

  const handleDeletePayment = (index) => {
    const copied = [...payments];
    const filtered = copied.filter((item, i) => i !== index);

    setPayments(filtered);
  };

  const handleTemplate = (index, amount, salary) => {
    setPayments([]);
    // setSalary(initTotalAmount);
    setActiveClass(index);
    const arr = [];

    console.log(learnYearAuto);

    const start = new Date(
      learnYearAuto.split(",")[0].split("/").reverse().join("-")
    );
    const end = new Date(
      learnYearAuto.split(",")[1].split("/").reverse().join("-")
    );

    const time_difference = end.getTime() - start.getTime();

    //calculate days difference by dividing total milliseconds in a day
    const days_difference = time_difference / (1000 * 60 * 60 * 24);
    console.log(start, end, time_difference, days_difference);

    const start_date = new Date(
      learnYearAuto.split(",")[0].split("/").reverse().join("-")
    );
    const totalDays = Math.floor(days_difference / amount);
    let paymentCounter = 0;

    for (let i = 0; i < amount; i++) {
      paymentCounter += totalDays;
      arr.push({
        id: i + 1,
        start_date: new Date(
          start_date.getTime() + paymentCounter * 24 * 60 * 60 * 1000
        ),
        end_date: new Date(
          start_date.getTime() +
            (paymentCounter * 24 * 60 * 60 * 1000 + threeMonths)
        ),
        amount: (salary / amount).toFixed(2),
      });
    }
    setIsManual(false);

    setPayments(arr);
  };

  const handleManual = () => {
    setPayments([]);
    setIsManual(true);
    setActiveClass(1);
    const arr = [];

    for (let i = 0; i < 2; i++) {
      arr.push({
        id: i + 1,
        start_date: "",
        amount: "",
      });
    }
    setPayments(arr);
  };

  const handleCancel = async () => {
    try {
      const response = await apiClientProtected().delete(
        `/finances/calendar/delete?user_id=${user_id}`
      );
      setPayments([
        { id: 1, start_date: "", end_date: "", amount: "" },
        { id: 2, start_date: "", end_date: "", amount: "" },
      ]);
    } catch (err) {
      console.log(err);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const fd = new FormData();
    fd.append("user_id", user_id);
    if (!pageInfo) {
      fd.append("edoc_template_id", edoc_template_id);
    }

    for (let i = 0; i < payments.length; i++) {
      for (let key in payments[i]) {
        if (key === "start_date" || key === "end_date") {
          fd.append(
            `deadlines[${i}][${key}]`,
            payments[i][key]
              ? dateFormat(payments[i][key], null, "/")
              : payments[i][key]
          );
        } else {
          fd.append(`deadlines[${i}][${key}]`, payments[i][key]);
        }
      }
    }

    console.log(pageInfo);
    // /finances/calendar/request
    try {
      const response = await apiClientProtected().post(
        "/finances/calendar/request",
        fd
      );
      console.log(response);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["req_sent"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      setIsSubmitting(false);
      setOpenModal(false);
      // if (!pageInfo) {
      //   setIndSchedule(response.data.individualSchedule);
      // }
    } catch (err) {
      setErrors(err.response.data.errors);
      setErrorText(err.response.data.errors);
      setIsSubmitting(false);
    }
  };

  return (
    <Wrapper>
      <Controller mainColor={mainColor}>
        <div className="button-group">
          <button
            className={`${activeClass === 1 ? "active" : ""} ctrl-btn`}
            onClick={handleManual}
          >
            {locale && langs[locale]["manual"]}
          </button>
          <button
            className={`${activeClass === 2 ? "active" : ""} ctrl-btn`}
            onClick={() => handleTemplate(2, 6, initTotalAmount)}
          >
            {locale && langs[locale]["template"]} 6
          </button>
          <button
            className={`${activeClass === 3 ? "active" : ""} ctrl-btn`}
            onClick={() => handleTemplate(3, 8, salary)}
          >
            {locale && langs[locale]["template"]} 8
          </button>
          <button
            className={`${activeClass === 4 ? "active" : ""} ctrl-btn`}
            onClick={() => handleTemplate(4, 10, salary)}
          >
            {locale && langs[locale]["template"]} 10
          </button>
        </div>
        <div className="button-group">
          <Amount salary={salary} mainColor={mainColor}>
            <span>{salary} ₾ </span>
          </Amount>
          {isManual && (
            <Counter
              onClick={handleAddPayment}
              size={"35px"}
              mainColor={mainColor}
            >
              <span className="counter-inner">
                <HiPlusSm color="#fff" size={18} />
              </span>
            </Counter>
          )}
        </div>
      </Controller>
      <form onSubmit={handleSubmit}>
        {payments.map((item, index) => (
          <FlexWrapper key={index}>
            <Counter size={"40px"} mainColor={mainColor}>
              <span className="counter-inner">{index + 1}</span>
            </Counter>
            <div className="form-div">
              <DatePicker
                name="start_date"
                className="form-item"
                placeholderText={locale && langs[locale]["choose_date"]}
                selected={payments[index].start_date}
                disabled={
                  !isManual ||
                  new Date().getTime() >
                    new Date(payments[index].start_date).getTime()
                }
                onChange={(date) => handleDate(date, "start_date", index)}
                dateFormat="dd-MM-yyyy"
                minDate={
                  new Date(
                    isManual && learnYearManual && learnYearAuto
                      ? learnYearManual
                          .split(",")[0]
                          .split("/")
                          .reverse()
                          .join("-")
                      : !isManual && learnYearManual && learnYearAuto
                      ? learnYearAuto
                          .split(",")[0]
                          .split("/")
                          .reverse()
                          .join("-")
                      : ""
                  )
                }
                maxDate={
                  new Date(
                    isManual && learnYearManual && learnYearAuto
                      ? learnYearManual
                          .split(",")[1]
                          .split("/")
                          .reverse()
                          .join("-")
                      : !isManual && learnYearManual && learnYearAuto
                      ? learnYearAuto
                          .split(",")[1]
                          .split("/")
                          .reverse()
                          .join("-")
                      : ""
                  )
                }
              />
              {errors && (
                <div className="text-danger">
                  {errors[`deadlines.${index}.start_date`]}
                </div>
              )}
              {/* {errors && <div className="text-danger">{errors.first_name}</div>} */}
            </div>
            <div className="form-div">
              <input
                type="text"
                name="amount"
                className="form-item"
                disabled={!isManual}
                placeholder={locale && langs[locale]["quantity"]}
                value={payments[index].amount}
                onChange={(e) => handleChange(e, index)}
                id="amount"
              />
              {errors && (
                <div className="text-danger">
                  {errors[`deadlines.${index}.amount`]}
                </div>
              )}
              {/* {errors && <div className="text-danger">{errors.first_name}</div>} */}
              {/* deadlines.0.amount */}
              {/* disabled={
                  !isManual ||
                  new Date().getTime() >
                    new Date(payments[index].start_date).getTime()
                } */}
            </div>

            {isManual && (
              <DeleteButton
                onClick={() => handleDeletePayment(index)}
                mainColor={mainColor}
              >
                <MdClose />
              </DeleteButton>
            )}
          </FlexWrapper>
        ))}
        <Footer mainColor={mainColor}>
          <button className="btn-form" type="submit">
            {isSubmitting ? <ButtonLoader /> : locale && langs[locale]["save"]}
          </button>
          {pageInfo && (
            <button
              className="btn-form btn-cancel"
              type="button"
              onClick={handleCancel}
            >
              {locale && langs[locale]["cancel_graphic"]}
            </button>
          )}
        </Footer>
        <div className="text-danger">{errorText["deadlines"]}</div>
      </form>
      {success && (
        <SweetAlert2
          {...swalProps}
          onConfirm={() => {
            setShowModal(false);
            setOpenModal(false);
          }}
        />
      )}
    </Wrapper>
  );
};

export default PaymentsForm;

const Wrapper = styled.div`
  width: 100%;
  /* background: #fff; */
  margin-top: 1rem;
`;

const Controller = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
  justify-content: space-between;
  .button-group {
    display: flex;
    gap: 0 0.5rem;
  }

  .ctrl-btn {
    background: #fff;
    border: ${(props) => `1px solid ${props.mainColor}`};
    color: ${(props) => props.mainColor};
    padding: 0.5rem 1.25rem;
    border-radius: 6px;
    &:hover {
      background: ${(props) => props.mainColor};
      color: #fff;
    }
  }
  .active {
    background: ${(props) => props.mainColor};
    color: #fff;
  }
  .add-button {
    border: 1px solid #009ef7;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    transition: all 300ms;
    box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.1);
    &:hover {
      background: #eee;
    }
  }
`;

const FlexWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0 1rem;
  position: relative;
  margin-bottom: 1rem;
  .form-div {
    width: 50%;
  }
  .form-item {
    border: 1px solid #ddd;
    padding: 1rem;
    border-radius: 6px;
    width: 100%;
  }
`;
const Counter = styled.div`
  background-color: #fff;
  padding: 0.25rem;
  border-radius: 50%;
  border: 1px solid #ddd;
  box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.1);
  .counter-inner {
    width: ${(props) => props.size};
    height: ${(props) => props.size};
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background: ${(props) => props.mainColor};
    cursor: pointer;
    color: #fff;
  }
`;

const Amount = styled.div`
  background-color: #fff;
  padding: 0.25rem;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.1);
  span {
    padding: 0.5rem 1.5rem;
    background: ${({ salary, mainColor }) =>
      salary < 0 ? "#ee4c4c" : `${mainColor}`};
    color: #fff;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

const DeleteButton = styled.div`
  cursor: pointer;
  width: 30px;
  height: 30px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: -10px;
  right: -10px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
`;

const Footer = styled.div`
  display: flex;
  justify-content: center;
  gap: 0 0.5rem;
  margin-top: 2rem;
  .btn-form {
    border-radius: 0.475rem;
    padding: 10.75px 20.5px;
    color: #fff;
    background: ${(props) => props.mainColor};
    &:hover {
      background: #e77489;
    }
  }
  .btn-cancel {
    background: #ee4c4c;
  }
`;
