import { useEffect, useState, useRef } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import { FormElement, FormItem, FlexElement, FileUpload } from "./../styles";
import { MdCloudUpload } from "react-icons/md";
import { MultiSelect } from "react-multi-select-component";

import { useTableContext } from "../../context/TableContext";
import SubmitLoader from "./../../ui/SubmitLoader";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import SweetAlert2 from "react-sweetalert2";

function AdminCreationForm({ fetchLink }) {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageSrc, setImageSrc] = useState("");
  const [cv, setCv] = useState("");
  const [programs, setPrograms] = useState([]);
  const [preview, setPreview] = useState("");
  const [fileName, setFileName] = useState("");
  const [swalProps, setSwalProps] = useState({});
  const [userAdded, setUserAdded] = useState(false);
  const [fields, setFields] = useState([]);
  const [selectField, setSelectField] = useState("1");

  const {
    handleDataSubmit,
    relationFields,
    setOpenModal,
    errors,
    setErrors,
    pageInfo,
  } = useTableContext();

  const [fieldData, setFieldData] = useState({
    first_name: "",
    last_name: "",
    identity_number: "",
    phone: "",
    email: "",
    school_id: "",
    administration_item_id: "",
    administration_position_id: "",
    photo: "",
    role_id: "",
    cv: "",
    program_ids: [],
  });

  const imageRef = useRef(null);
  const cvRef = useRef(null);

  // set directions array on init
  useEffect(() => {
    const opts = [];
    relationFields["program"] &&
      Object.keys(relationFields["program"].options).map((opt) =>
        opts.push({
          label: relationFields["program"].options[opt],
          value: opt,
        })
      );
    setPrograms(opts);
  }, []);

  useEffect(() => {
    if (imageSrc) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(imageSrc);
    }
  }, [imageSrc]);

  // const { relationFields } = useTableContext()
  const handleChange = (e) => {
    if (e.target.name === "photo") {
      setImageSrc(e.target.files[0]);
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    } else if (e.target.name === "cv") {
      setCv(e.target.files[0]);
      setFieldData({ ...fieldData, [e.target.name]: e.target.files[0] });
    } else if (e.target.name === "school_id") {
      setFieldData({
        ...fieldData,
        [e.target.name]: e.target.value,
        administration_item_id: "",
      });
    } else if (e.target.name === "administration_item_id") {
      setFieldData({
        ...fieldData,
        [e.target.name]: e.target.value,
        school_id: "",
      });
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleProgram = (value) => {
    setFieldData({ ...fieldData, program_ids: value });
  };

  console.log(relationFields);
  const triggerFile = (name) => {
    if (name === "cv") {
      cvRef.current.click();
    } else {
      imageRef.current.click();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const fd = new FormData();
    setIsSubmitting(true);

    for (let key in fieldData) {
      if (key === "program_ids") {
        for (let i = 0; i < fieldData[key].length; i++) {
          console.log(fieldData[key][i].value);
          fd.append(`program_ids[${i}]`, fieldData[key][i].value);
        }
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(fetchLink, fd);
      pageInfo.routeName === "library-subject"
        ? handleDataSubmit(response.data.data)
        : handleDataSubmit(response.data);
      setOpenModal(false);
      setErrors(null);
      setIsSubmitting(false);
      setUserAdded(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data);
      console.log(err.response);
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <FormItem>
          <h6>{locale && langs[locale]["personal_info"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="first_name">
                {locale && langs[locale]["first_name"]}
              </label>
              <input
                type="text"
                name="first_name"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["first_name"]}
                id="first_name"
                value={fieldData.first_name}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.first_name}</div>}
            </div>

            <div>
              <label htmlFor="last_name">
                {locale && langs[locale]["last_name"]}
              </label>
              <input
                type="text"
                name="last_name"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["last_name"]}
                id="last_name"
                value={fieldData.last_name}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.last_name}</div>}
            </div>
          </FlexElement>

          <FlexElement>
            <div>
              <label htmlFor="identity_number">
                {locale && langs[locale]["id_number"]}
              </label>
              <input
                type="text"
                name="identity_number"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["id_number"]}
                id="identity_number"
                value={fieldData.identity_number}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.identity_number}</div>
              )}
            </div>
          </FlexElement>
        </FormItem>
        <FormItem>
          <h6>{locale && langs[locale]["contact_info"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="phone">{locale && langs[locale]["phone"]}</label>
              <input
                type="text"
                name="phone"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["phone"]}
                id="phone"
                value={fieldData.phone}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.phone}</div>}
            </div>

            <div>
              <label htmlFor="email">{locale && langs[locale]["email"]}</label>
              <input
                type="text"
                name="email"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["email"]}
                id="email"
                value={fieldData.email}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.email}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["others"]}</h6>
          <FlexElement>
            <div className="form-group">
              <label htmlFor="administration_position_id">
                {locale && langs[locale]["admin_position"]}
              </label>
              <select
                className="form-control mb-3 form-control form-control-solid "
                name="administration_position_id"
                id="administration_position_id"
                value={fieldData.administration_position_id}
                onChange={handleChange}
              >
                <option value="" key="13246798asd">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {relationFields["positions"] &&
                  Object.keys(relationFields["positions"].options).map(
                    (field, index) => (
                      <option value={field} key={index}>
                        {relationFields["positions"].options[field]}
                      </option>
                    )
                  )}
              </select>
              {errors && (
                <div className="text-danger">
                  {errors.administration_position_id}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="selectField">
                {locale && langs[locale]["choose_unit"]}
              </label>
              <select
                className="form-control mb-3 form-control form-control-solid "
                name="selectField"
                id="selectField"
                value={selectField}
                onChange={(e) => setSelectField(e.target.value)}
              >
                <option value="" key="13246798asd">
                  {locale && langs[locale]["choose_item"]}
                </option>
                <option value="1" key="1">
                  {locale && langs[locale]["school"]}
                </option>
                <option value="2" key="2">
                  {locale && langs[locale]["admin_units"]}
                </option>
              </select>
            </div>
          </FlexElement>

          <FlexElement>
            {selectField === "1" ? (
              <div className="form-group">
                <label htmlFor="school_id">
                  {locale && langs[locale]["school"]}
                </label>
                <select
                  className="form-control mb-3 form-control form-control-solid "
                  name="school_id"
                  id="school_id"
                  value={fieldData.school_id}
                  onChange={handleChange}
                >
                  <option value="">
                    {locale && langs[locale]["choose_item"]}
                  </option>
                  {relationFields["school"] &&
                    Object.keys(relationFields["school"].options).map(
                      (field, index) => (
                        <option value={field} key={index}>
                          {relationFields["school"].options[field]}
                        </option>
                      )
                    )}
                </select>
                {errors && (
                  <div className="text-danger">{errors.school_id}</div>
                )}
              </div>
            ) : (
              <div className="form-group">
                <label htmlFor="administration_item_id">
                  {locale && langs[locale]["admin_units"]}
                </label>
                <select
                  className="form-control mb-3 form-control form-control-solid "
                  name="administration_item_id"
                  id="administration_item_id"
                  value={fieldData.administration_item_id}
                  onChange={handleChange}
                >
                  <option value="" key="13246798asd">
                    {locale && langs[locale]["choose_item"]}
                  </option>
                  {relationFields["items"] &&
                    Object.keys(relationFields["items"].options).map(
                      (field, index) => (
                        <option value={field} key={index}>
                          {relationFields["items"].options[field]}
                        </option>
                      )
                    )}
                </select>
                {errors && (
                  <div className="text-danger">
                    {errors.administration_item_id}
                  </div>
                )}
              </div>
            )}
          </FlexElement>
          <FlexElement>
            <div className="form-group">
              <label htmlFor="role_id">
                {locale && langs[locale]["roles"]}
              </label>
              <select
                className="form-control mb-3 form-control form-control-solid "
                name="role_id"
                id="role_id"
                value={fieldData.role_id}
                onChange={handleChange}
              >
                <option value="" key="13246798asd">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {relationFields["roles"] &&
                  Object.keys(relationFields["roles"].options).map(
                    (field, index) => (
                      <option value={field} key={index}>
                        {relationFields["roles"].options[field]}
                      </option>
                    )
                  )}
              </select>
              {errors && <div className="text-danger">{errors.role_id}</div>}
            </div>
            <div>
              <label htmlFor="program">
                {locale && langs[locale]["program"]}
              </label>
              <MultiSelect
                options={programs}
                value={fieldData.program_ids}
                onChange={handleProgram}
                labelledBy={locale && langs[locale]["choose_item"]}
                isCreatable={false}
              />
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <h6>{locale && langs[locale]["attached_files"]}</h6>

          <FlexElement>
            <div className="form-group">
              <label htmlFor="administration_item_id">
                {locale && langs[locale]["photo"]}
              </label>
              <input
                type="file"
                name="photo"
                ref={imageRef}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="image/png, image/jpg, image/jpeg"
              />
              <FileUpload
                onClick={() => triggerFile("photo")}
                style={{ backgroundImage: `url(${preview})` }}
              >
                <MdCloudUpload size={16} />
                {locale && langs[locale]["file_upload"]}
              </FileUpload>
              {/* <div>
                      <img src={preview} alt="" />
                    </div> */}
              {errors && <div className="text-danger">{errors.photo}</div>}
            </div>
            <div className="form-group">
              <label htmlFor="administration_item_id">cv</label>
              <FileUpload onClick={() => triggerFile("cv")}>
                <MdCloudUpload size={16} />
                {cv ? `${cv.name}` : locale && langs[locale]["file_upload"]}
              </FileUpload>
              <input
                type="file"
                name="cv"
                ref={cvRef}
                style={{ display: "none" }}
                onChange={handleChange}
                accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
              />

              {errors && <div className="text-danger">{errors.cv}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["save"]}
            </button>
          )}
        </div>
      </FormElement>

      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default AdminCreationForm;
