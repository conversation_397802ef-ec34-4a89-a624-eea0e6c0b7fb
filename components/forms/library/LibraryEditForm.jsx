import { useState, useEffect } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import BaseDropzone from "../../base/BaseDropzone";
import BaseFilterSelect from "../../base/BaseFilterSelect";
import { useRouter } from "next/router";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import { useTableContext } from "../../context/TableContext";
import SweetAlert2 from "react-sweetalert2";

const LibraryEditForm = ({ bookId }) => {
  const { relationFields, errors, setErrors, handleDataSubmit } =
    useTableContext();
  const router = useRouter();
  const { locale } = useLocaleContext();

  const [book, setBook] = useState({
    title: "",
    author: "",
    file_name: "",
    lecturer_id: "",
    topic: "",
    published_date: "",
    subject: "",
  }); //es aris struktura , es tu scorad gekneba martivad mikvebi . file_names gaakete da masivi , analogiurad topics da masivi , index unda mianichon

  const [relations, setRelations] = useState([]);
  const [lecturers, setLecturers] = useState([]);
  const [swalProps, setSwalProps] = useState({});
  const [userEdited, setUserEdited] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [percent, setPercent] = useState(0);

  useEffect(() => {
    const getRelations = async () => {
      const response = await apiClientProtected().get("/el-books");
      const book = response.data.data.find(
        (item) => item.id === Number(bookId)
      );

      const date = book.published_date && book.published_date.slice(0, 10);

      console.log(response.data.data);

      let relationsCopy = { ...response.data.topics };
      console.log(relationsCopy, book, "asdqw");
      relationsCopy = Object.entries(relationsCopy).map((item) => {
        return { id: item[0], title: item[1], label: item[1] };
      });
      const lecturer =
        book.lecturer && book.lecturer !== " "
          ? response.data.lecturers.find(
              (item) => `${item.first_name} ${item.last_name}` === book.lecturer
            ).id
          : null;
      console.log(relationsCopy);
      const topicsId =
        book.topic.length &&
        relationsCopy.find((item) => item.title === book.topic[0]).id;
      const filesArray = Object.entries(book.files).map((item) => {
        return { name: item[1], url: item[0] };
      });
      console.log(filesArray);
      setRelations(relationsCopy);
      const lecturers = response.data.lecturers.map((item) => {
        item.label = item.first_name + " " + item.last_name;
        return item;
      });
      setLecturers(lecturers);
      setBook({
        ...book,
        published_date: date,
        topic: [topicsId],
        deletedFiles: [],
        lecturer_id: lecturer,
        files: filesArray,
      });
    };
    getRelations();
  }, []);

  const handleChange = (e) => {
    if (e.target.name === "file_name") {
      setBook({ ...book, [e.target.name]: e.target.files[0] });
    } else if (e.target.name === "topic") {
      const topicsArr = [];
      topicsArr.push(e.target.value);
      setBook({ ...book, [e.target.name]: topicsArr });
    } else if (e.target.name === "published_date") {
      setBook({ ...book, [e.target.name]: e.target.value });
    } else {
      console.log(e.target.value);
      setBook({ ...book, [e.target.name]: e.target.value });
    }
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    // console.log(name, arrData); return
    setBook({ ...book, [name]: arrData });
  };

  const handleDrop = (files) => {
    setBook((prev) => {
      return {
        ...prev,
        files: [...prev.files, ...files],
      };
    });
  };

  const handleFileDelete = (fileIndex) => {
    setBook((prev) => {
      const d = prev.files.filter((item, index) => index !== fileIndex);
      const removedElement = prev.files.find(
        (item, index) => index === fileIndex && item.url !== undefined
      );
      // console.log(removedElement, 'Daviggale upalo')
      return {
        ...book,
        files: d,
        deletedFiles: [
          ...book.deletedFiles,
          removedElement && removedElement.url,
        ],
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const date = book.published_date
      ? book.published_date.split("-").reverse().join("-").toString()
      : "";

    const fd = new FormData();
    fd.append("_method", "PUT");
    fd.append("title", book.title);
    fd.append("author", book.author);
    fd.append("file_name", book.files);
    fd.append("lecturer_id", book.lecturer_id);
    fd.append("published_date", date);
    fd.append("subject", book.subject);

    for (let key of book.topic) {
      console.log(key, "TOPICS");
      fd.append("topics[]", key);
    }

    for (let i = 0; i < book.deletedFiles.length; i++) {
      // console.log(key, 'TOPICS');
      fd.append(`deleted_files[]`, book.deletedFiles[i]);
    }

    for (let key of book.files) {
      console.log(key);
      if (key.size > 0) {
        fd.append("files[]", key);
      }
    }

    try {
      const response = await apiClientProtected().post(
        `/el-books/${book.id}`,
        fd,
        {
          onUploadProgress: (progressEvent) => {
            const { loaded, total } = progressEvent;
            console.log("asdasd", loaded, total);
            let p = Math.floor((loaded * 100) / total);
            setPercent(p);
          },
        }
      );
      console.log(response.data.data, "response data");
      setIsSubmitting(false);
      setUserEdited(true);

      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      router.push("/admin/library");
    } catch (err) {
      console.log(err.response);
      setIsSubmitting(false);
      setErrors(err.response.data);
      // setOpenModal(false)
    }
  };

  return (
    <div className="container mt-10 library__add-form">
      <form onSubmit={handleSubmit}>
        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="title"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["title"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="title"
              value={book.title}
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["title"]}
              id="title"
            />
            {errors && <div className="text-danger">{errors.title}</div>}
          </div>
          <div className="form-group">
            <label
              htmlFor="author"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["author"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="author"
              value={book.author}
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["author"]}
              id="author"
            />
            {errors && <div className="text-danger">{errors.author}</div>}
          </div>
        </div>
        <div>
          {/* <label htmlFor='file_name' className="my-3 pointer cursor-pointer d-flex align-items-center">
            <span className="required">წიგნის ატვირთვა</span>
            <img src="/icons/exclamation.svg" alt="required" width='12' className="ms-1" />
          </label>
          <input
            type='file'
            name='file_name'
            multiple={true}
            onChange={handleChange}
            className="form-control mb-3 form-control form-control-solid "
            placeholder='წიგნის სათაური'
            id='file_name'
          /> */}
        </div>
        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="file_name"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["lecturer"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <BaseFilterSelect
              data={lecturers}
              setValue={handleFilterValue}
              name="lecturer_id"
              searchable={true}
              multiSelect={false}
              placeholder={locale && langs[locale]["lecturer"]}
              defaultValue={book.lecturer_id}
            />
            {errors && <div className="text-danger">{errors.lecturer_id}</div>}
          </div>

          <div className="form-group">
            <label
              htmlFor="topic"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["topic"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <BaseFilterSelect
              data={relations}
              name="topic"
              searchable={true}
              setValue={handleFilterValue}
              multiSelect={true}
              placeholder={locale && langs[locale]["topic"]}
              defaultValue={book.topic}
            />
            {errors && <div className="text-danger">{errors.topics}</div>}
          </div>
        </div>
        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="file_name"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["release_date"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="date"
              name="published_date"
              onChange={handleChange}
              defaultValue={book.published_date}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["release_date"]}
              id="published_date"
            />
            {errors && (
              <div className="text-danger">{errors.published_date}</div>
            )}
          </div>
          <div className="form-group">
            <label
              htmlFor="subject"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["subject"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="subject"
              value={book.subject}
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["subject"]}
              id="subject"
            />
            {errors && <div className="text-danger">{errors.subject}</div>}
          </div>
        </div>

        <div>
          <label
            htmlFor="file_name"
            className="my-3 pointer cursor-pointer d-flex align-items-center"
          >
            <span className="required">
              {locale && langs[locale]["book_upload"]}
            </span>
            <img
              src="/icons/exclamation.svg"
              alt="required"
              width="12"
              className="ms-1"
            />
          </label>
          <BaseDropzone
            handleDrop={handleDrop}
            handleFileDelete={handleFileDelete}
            uploadPercent={percent}
            files={book.files}
          />
          {errors && <div className="text-danger">{errors.files}</div>}
        </div>

        <div className="d-flex align-items-center justify-content-center mt-4">
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </div>
      </form>
      {userEdited && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </div>
  );
};

export default LibraryEditForm;
