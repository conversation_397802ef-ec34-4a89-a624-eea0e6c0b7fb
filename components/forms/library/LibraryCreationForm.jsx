import { useState, useEffect } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import BaseDropzone from "../../base/BaseDropzone";
import BaseFilterSelect from "../../base/BaseFilterSelect";
import { useRouter } from "next/router";
import { langs } from "./../../locale";
import { useLocaleContext } from "./../../context/LocaleContext";

import { useTableContext } from "../../context/TableContext";
import SweetAlert2 from "react-sweetalert2";

const LibraryCreationForm = () => {
  const { locale } = useLocaleContext();
  const {
    setRelationFields,
    relationFields,
    handleDataSubmit,
    setOpenModal,
    errors,
    setErrors,
  } = useTableContext();
  const router = useRouter();

  const [data, setData] = useState({
    title: "",
    author: "",
    file_name: [],
    lecturer_id: "",
    topics: "",
    published_date: "",
    subject: "",
  });

  console.log(locale);

  const [relations, setRelations] = useState([]);
  const [lecturers, setLecturers] = useState([]);
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [percent, setPercent] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const getRelations = async () => {
      const response = await apiClientProtected().get("/el-books");
      console.log(response, "hamar hamar");
      setRelationFields({
        topics: {
          options: response.data.topics,
          name: "თემატიკა",
        },
        lecturers: {
          options: response.data.lecturers,
          name: "ლექტორები",
        },
      });

      let relationsCopy = { ...response.data.topics };
      relationsCopy = Object.entries(relationsCopy).map((item) => {
        return { id: item[0], title: item[1], label: item[1] };
      });

      setRelations(relationsCopy);
      const lecturers = response.data.additional.lecturers.map((item) => {
        item.label = item.first_name + " " + item.last_name;
        return item;
      });
      setLecturers(lecturers);
    };
    getRelations();
  }, []);

  const handleChange = (e) => {
    if (e.target.name === "file_name") {
      setData({ ...data, [e.target.name]: e.target.files });
    } else if (e.target.name === "topics") {
      const topicsArr = [];
      topicsArr.push(e.target.value);
      setData({ ...data, [e.target.name]: topicsArr });
    } else if (e.target.name === "published_date") {
      const date = e.target.value.split("-").reverse().join("-");
      setData({ ...data, [e.target.name]: date });
    } else {
      setData({ ...data, [e.target.name]: e.target.value });
    }
  };

  const handleDrop = (files) => {
    setData((prev) => {
      return {
        ...prev,
        file_name: [...prev.file_name, ...files],
      };
    });
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    setData({ ...data, [name]: arrData });
  };

  const handleFileDelete = (i) => {
    setData((prev) => {
      const d = prev.file_name.filter((item, index) => index !== i);
      return { ...data, file_name: d };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    let responseArray = [];
    const fd = new FormData();
    fd.append("title", data.title.trim());
    fd.append("author", data.author.trim());
    fd.append("file_name", data.file_name);
    fd.append("lecturer_id", data.lecturer_id);
    fd.append("published_date", data.published_date);
    fd.append("subject", data.subject);

    for (let key of data.topics) {
      console.log(key, "TOPICS");
      fd.append("topics[]", key);
    }

    for (let key of data.file_name) {
      console.log(key, "file_name");
      fd.append("files[]", key);
    }

    try {
      const response = await apiClientProtected().post("/el-books", fd, {
        onUploadProgress: (progressEvent) => {
          const { loaded, total } = progressEvent;
          console.log("asdasd", loaded, total);
          let p = Math.floor((loaded * 100) / total);
          setPercent(p);
        },
      });
      console.log(response.data.data, "response data");
      setIsSubmitting(false);
      // handleDataSubmit(response.data)
      setSuccess(true);
      setOpenModal(false);
      setErrors(null);

      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      router.push("/admin/library");
    } catch (err) {
      setErrors(err.response.data);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mt-10 library__add-form">
      <form onSubmit={handleSubmit}>
        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="title"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["title"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="title"
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["title"]}
              id="title"
            />
            {errors && <div className="text-danger">{errors.title}</div>}
          </div>
          <div className="form-group">
            <label
              htmlFor="author"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["author"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="author"
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["author"]}
              id="author"
            />
            {errors && <div className="text-danger">{errors.author}</div>}
          </div>
        </div>
        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="file_name"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["lecturer"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <BaseFilterSelect
              data={lecturers}
              name="lecturer_id"
              setValue={handleFilterValue}
              searchable={true}
              multiSelect={false}
              placeholder={locale && langs[locale]["lecturer"]}
            />
            {errors && <div className="text-danger">{errors.lecturer_id}</div>}
          </div>

          <div className="form-group">
            <label
              htmlFor="file_name"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["topic"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <BaseFilterSelect
              data={relations}
              name="topics"
              searchable={true}
              setValue={handleFilterValue}
              multiSelect={true}
              placeholder={locale && langs[locale]["topic"]}
            />
            {errors && <div className="text-danger">{errors.topics}</div>}
          </div>
        </div>

        <div className="d-flex gap-4">
          <div className="form-group">
            <label
              htmlFor="published_date"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["release_date"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="date"
              name="published_date"
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["release_date"]}
              id="published_date"
            />
            {errors && (
              <div className="text-danger">{errors.published_date}</div>
            )}
          </div>
          <div className="form-group">
            <label
              htmlFor="subject"
              className="my-3 pointer cursor-pointer d-flex align-items-center"
            >
              <span className="required">
                {locale && langs[locale]["subject"]}
              </span>
              <img
                src="/icons/exclamation.svg"
                alt="required"
                width="12"
                className="ms-1"
              />
            </label>
            <input
              type="text"
              name="subject"
              onChange={handleChange}
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["subject"]}
              id="subject"
            />
            {errors && <div className="text-danger">{errors.subject}</div>}
          </div>
        </div>

        <div>
          <label
            htmlFor="file_name"
            className="my-3 pointer cursor-pointer d-flex align-items-center"
          >
            <span className="required">
              {locale && langs[locale]["book_upload"]}
            </span>
            <img
              src="/icons/exclamation.svg"
              alt="required"
              width="12"
              className="ms-1"
            />
          </label>
          <BaseDropzone
            handleDrop={handleDrop}
            handleFileDelete={handleFileDelete}
            uploadPercent={percent}
            files={data.file_name}
          />
          {errors && <div className="text-danger">{errors.files}</div>}
        </div>

        <div className="d-flex align-items-center justify-content-center mt-4">
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["create"]}
            </button>
          )}
        </div>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </div>
  );
};

export default LibraryCreationForm;
