import { useState, useEffect } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import styled from "styled-components";
import Link from "next/link";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../../context/TableContext";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

const SurveysList = ({ syllabusId, lecturers }) => {
  const { locale } = useLocaleContext();
  const [surveys, setSurveys] = useState([]);
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const { setOpenModal } = useTableContext();

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get(
          `/surveys/survey/status?syllabus_id=${syllabusId}`
        );
        console.log(response);
        setSurveys(response.data.surveys);
      } catch (err) {
        console.log(err);
      }
    })();
  }, []);

  const handleSubmit = async (e, id) => {
    const fd = new FormData();

    fd.append("syllabus_id", syllabusId);
    fd.append("survey_id", id);
    fd.append("status", e.target.checked ? 1 : 0);

    console.log(e.target.id, typeof e.target.id);

    const copied = [...surveys];
    const mapped = copied.map((item) => {
      if (item.id === Number(e.target.id) && item.status === 1) {
        item.status = 0;
      } else if (item.id === Number(e.target.id) && item.status === 0) {
        item.status = 1;
      }
      return item;
    });

    setSurveys(mapped);

    try {
      const response = await apiClientProtected().post(
        `/surveys/activation/set`,
        fd
      );
      setSuccess(true);
      setSwalProps({
        show: true,
        title: e.target.checked
          ? "გამოკითხვა გააქტიურდა"
          : "გამოკითხვა გაითიშა",
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      console.log(err);
    }
  };
  return (
    <div>
      <LecturersList>
        {lecturers.map((item) => (
          <li>
            <span>
              {item.first_name} {item.last_name}
            </span>
            {surveys.filter((item) => item.status === 1).length ? (
              <Link
                href={`https://api.portal.gipa.ge/api/surveys/analysis?survey_id=${
                  surveys.filter((item) => item.status === 1)[0].id
                }&syllabus_id=${syllabusId}&lecturer_id=${item.id}`}
              >
                <a className="lecturer-link" target="_blank">
                  შედეგების ნახვა
                </a>
              </Link>
            ) : null}
          </li>
        ))}
      </LecturersList>
      <SurveyList>
        {surveys.map((item, index) => (
          <li key={index}>
            <SurveyItem>
              <span>{item.name}</span>
              <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id={item.id}
                  name="mobility"
                  disabled={
                    item.status === 0 &&
                    surveys.filter((item) => item.status === 1).length
                  }
                  checked={item.status === 1}
                  onChange={(e) => handleSubmit(e, item.id)}
                />
              </div>
            </SurveyItem>
          </li>
        ))}
      </SurveyList>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </div>
  );
};

export default SurveysList;

const LecturersList = styled.ul`
  position: relative;
  border-bottom: 1px solid #ddd;
  margin-bottom: 1rem;
  li {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
`;

const SurveyList = styled.ul`
  position: relative;
  li {
    padding: 0.5rem;
  }
`;

const SurveyItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
