import { useState, useEffect } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import { FormItem, FlexElement } from "../styles";
import { useRouter } from "next/router";
import { useTableContext } from "../../context/TableContext";
import { useLocaleContext } from "../../context/LocaleContext";
import { langs } from "../../locale";
import styled from "styled-components";
import SweetAlert2 from "react-sweetalert2";

const EdocTable = ({ userId, pageInfo }) => {
  const router = useRouter();
  const { locale } = useLocaleContext();
  const { errors, setErrors } = useTableContext();
  const [typeId, setTypeId] = useState(null);
  const [edocs, setEdocs] = useState([]);
  const [docIndex, setDocIndex] = useState(0);
  const [openPopup, setOpenPopup] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [fieldData, setFieldData] = useState({
    comment: "",
    edoc_template_id: "",
    user_id: userId,
  });

  useEffect(() => {
    if (pageInfo.routeName === "students") {
      setTypeId(3);
    } else if (pageInfo.routeName === "lecturers") {
      setTypeId(2);
    } else {
      setTypeId(1);
    }
  }, []);

  useEffect(() => {
    const getEdocs = async () => {
      console.log(pageInfo);

      const response = await apiClientProtected().get("/edoc-templates");
      console.log(response);
      setEdocs(
        response.data.templates.data.filter((item) => item.user_type_id === typeId)
      );
    };

    getEdocs();
  }, [typeId]);

  const handleEdoc = async (id) => {
    const fd = new FormData();

    for (let key in fieldData) {
      fd.append(key, fieldData[key]);
    }
    fd.append("edoc_template_id", id);

    try {
      const response = await apiClientProtected().post("/edoc/create", fd);
      console.log(response);
      setSuccess(true);
      setErrors(null);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      router.push(`/admin/edoc/edit/${response.data.id}`);
    } catch (error) {
      setErrors(error.response.data.errors);
    }
  };

  return (
    <>
      <Table>
        <thead>
          <tr>
            <th>id</th>
            <th>სათაური</th>
            <th>მოქმედება</th>
          </tr>
        </thead>
        <tbody>
          {edocs.map((item, index) => (
            <tr key={item.id}>
              <td>{item.id}</td>
              <td>{item.name}</td>
              <td style={{ position: "relative" }}>
                <button
                  className="prepare-button"
                  onClick={() => {
                    setDocIndex(index);
                    setOpenPopup(true);
                  }}
                >
                  მომზადება
                </button>
                <Popup openPopup={openPopup} index={index} docIndex={docIndex}>
                  <p>ნამდვილად გსურთ დოკუმენტის მომზადება</p>
                  <div>
                    <button onClick={() => handleEdoc(item.id)}>დიახ</button>
                    <button onClick={() => setOpenPopup(false)}>არა</button>
                  </div>
                </Popup>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
};

export default EdocTable;

const Table = styled.table`
  tr {
    text-align: left !important;
  }
  tr td:last-child,
  tr th:last-child {
    text-align: right !important;
  }
  .prepare-button {
    background: #f5f8fa;
    color: #7e8299;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    &:hover {
      background: #eaf0f5;
      color: #009ef7;
    }
  }
`;

const Popup = styled.div`
  width: 300px;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  position: absolute;
  text-align: center;
  bottom: -160%;
  background: #fff;
  right: 0;
  transform: ${(props) =>
    props.openPopup && props.index === props.docIndex
      ? "scale(1)"
      : "scale(0)"};
  transform-origin: top right;
  transition: all 200ms;
  z-index: 100;
  div {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-top: 8px;
  }
  button {
    padding: 4px 12px;
    background: #eee;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
`;
