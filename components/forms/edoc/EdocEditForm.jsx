import { useEffect, useState, useRef } from "react";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "../../ui/SubmitLoader";
import { useTableContext } from "../../context/TableContext";
import { Editor } from "@tinymce/tinymce-react";
import { langs } from "../../locale";
import { useLocaleContext } from "../../context/LocaleContext";

import dynamic from "next/dynamic";
const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

import styled from "styled-components";
import SweetAlert2 from "react-sweetalert2";

const adminPersons = [
  { id: 1, name: "რექტორი" },
  { id: 1, name: "რექტორის ასისტენტი" },
  { id: 1, name: "დეკანი" },
];

const USER_TYPES = [
  { id: 1, name: "administrattor" },
  { id: 2, name: "lecturer" },
  { id: 3, name: "student" },
];

function EdocEditForm({ fetchLink, data, handleDataEdit, setOpenModal }) {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [userAdded, setUserAdded] = useState(false);

  const editorRef = useRef(null);

  const { errors, setErrors, relationFields, handleDataSubmit } =
    useTableContext();

  const [fieldData, setFieldData] = useState({
    name: "",
    index: "",
    text: "",
    automatic: "0",
    user_type_id: "",
    lang: "",
  });

  useEffect(() => {
    setFieldData({ ...data });
  }, []);

  const handleChange = async (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleEditorChange = (e, name) => {
    setFieldData({ ...fieldData, [name]: e.target.getContent() });
    console.log(name, e.target.getContent());
  };

  const handleEditor = (value, name) => {
    setFieldData({ ...fieldData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    fd.append("_method", "PUT");
    for (const key in fieldData) {
      if (fieldData[key] === "automatic") {
        fd.append(key, Number(fieldData[key]));
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post(
        `${fetchLink}/${fieldData.id}`,
        fd
      );

      handleDataEdit(response.data);
      setIsSubmitting(false);
      setErrors(null);
      setUserAdded(true);
      setOpenModal(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setIsSubmitting(false);
      setErrors(err.response.data);
      if (err) {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <>
      <FormElement onSubmit={handleSubmit}>
        <FormItem>
          <h6>{locale && langs[locale]["main_info"]}</h6>
          <FlexElement>
            <div>
              <label htmlFor="name">{locale && langs[locale]["title"]}</label>
              <input
                type="text"
                name="name"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["title"]}
                id="name"
                value={fieldData.name}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.name}</div>}
            </div>
            <div>
              <label htmlFor="index">{locale && langs[locale]["index"]}</label>
              <input
                type="text"
                name="index"
                className="form-control mb-3 form-control form-control-solid "
                placeholder={locale && langs[locale]["index"]}
                id="index"
                value={fieldData.index}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.index}</div>}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="automatic" style={{ display: "block" }}>
                {locale && langs[locale]["automatic"]}
              </label>
              <div className="form-check form-switch form-check-custom mb-4 form-check-solid">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="automatic"
                  name="automatic"
                  checked={
                    fieldData.automatic === 1 || fieldData.automatic === "1"
                  }
                  onChange={handleChange}
                />
                <span className="form-check-label fw-bold text-muted">
                  {fieldData.automatic === "0" || fieldData.automatic === 0
                    ? locale && langs[locale]["no"]
                    : locale && langs[locale]["yes"]}
                </span>
              </div>
            </div>
            <div>
              <label htmlFor="lang" style={{ display: "block" }}>
                {locale && langs[locale]["language"]}
              </label>
              <div className="form-check form-switch form-check-custom form-check-solid">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="lang"
                  name="lang"
                  checked={fieldData.lang === 1 || fieldData.lang === "1"}
                  onChange={handleChange}
                />
                <span className="form-check-label fw-bold text-muted">
                  {fieldData.lang === "0"
                    ? locale && langs[locale]["georgian"]
                    : locale && langs[locale]["english"]}
                </span>
              </div>
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="user_type_id" style={{ display: "block" }}>
                {locale && langs[locale]["status"]}
              </label>
              <select
                className="form-control mb-3 form-control form-control-solid"
                name="user_type_id"
                value={fieldData.user_type_id}
                onChange={handleChange}
              >
                <option value="" key="asd21">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {USER_TYPES.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
              {errors && (
                <div className="text-danger">{errors.user_type_id}</div>
              )}
            </div>

            <div>
              <label htmlFor="signature" style={{ display: "block" }}>
                {locale && langs[locale]["signature"]}
              </label>
              <select
                className="form-control mb-3 form-control form-control-solid"
                name="signature"
                value={fieldData.signature}
                onChange={handleChange}
              >
                <option value="" key="asd21">
                  {locale && langs[locale]["choose_item"]}
                </option>
                {adminPersons.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
              {errors && <div className="text-danger">{errors.signature}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <FormItem>
          <FlexElement>
            <div>
              <label htmlFor="">{locale && langs[locale]["description"]}</label>
              <ReactQuill
                theme="snow"
                placeholder={locale && langs[locale]["type_text"]}
                id="text"
                value={fieldData.text}
                onChange={(value) => handleEditor(value, "text")}
              />
              {errors && <div className="text-danger">{errors.text}</div>}
            </div>
          </FlexElement>
        </FormItem>

        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            type="button"
            onClick={() => {
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              {locale && langs[locale]["edit"]}
            </button>
          )}
        </div>
      </FormElement>

      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </>
  );
}

export default EdocEditForm;

const FormElement = styled.form`
  max-width: 800px;
  margin-top: 2rem;
`;

const FormItem = styled.div`
  margin-bottom: 1.75rem;
  h6 {
    margin-bottom: 0.75rem;
  }
`;
const FlexElement = styled.div`
  display: flex;
  gap: 1rem;
  div {
    flex: 1;
  }
  label {
    margin-bottom: 0.5rem;
    white-space: nowrap;
    display: block;
    text-align: left;
  }
`;

const CvLogo = styled.div`
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  img {
    width: 100%;
  }
`;

const FileUpload = styled.div`
  padding: 2rem 1rem;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: 4px;
  background: #f1faff;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  svg {
    color: #555;
  }
  &:hover {
    background: #009ef7;
    color: #fff;
  }
  &:hover svg {
    color: #fff;
  }
`;
