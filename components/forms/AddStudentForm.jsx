import { useState, useEffect } from "react";
import apiClientProtected from "../../helpers/apiClient";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import styled from "styled-components";
import SubmitLoader from "../ui/SubmitLoader";
import { MdOutlineDelete } from "react-icons/md";
import { WEEK_DAYS } from "./../sylabus/silabusData";

const AddStudentForm = ({ syllabusId, url }) => {
  const {
    errors,
    setErrors,
    setOpenModal,
    setData,
    setAlertMessage,
    signsDataUpdate,
    setSignsDataUpdate,
  } = useTableContext();
  const { locale } = useLocaleContext();
  const [students, setStudents] = useState([]);
  const [pickedStudents, setPickedStudents] = useState([]);
  const [studentIds, setStudentIds] = useState([]);
  const [lectureTimes, setLectureTimes] = useState([]);
  const [searchString, setSearchString] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timesId, setTimesId] = useState([]);
  const [errorString, setErrorString] = useState("");

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      const response = await apiClientProtected().get(
        `/administration/search-students?keyword=${searchString}`
      );
      const timesResponse = await apiClientProtected().get(
        `/curriculum/lecture-times/${syllabusId}`
      );
      console.log(response, timesResponse, "Tom Sawyer");
      setLectureTimes(timesResponse.data.data);
      setStudents(response.data.data);
      setIsLoading(false);
    })();
  }, [searchString]);

  const handlePick = (data) => {
    const arr = [];
    const ids = [];
    arr.push(data);
    ids.push(data.id);
    setPickedStudents([...pickedStudents, ...arr]);
    setStudentIds([...studentIds, ...ids]);
    setSearchString("");
  };

  const handleTimes = (e) => {
    let arr = [];
    if (e.target.checked) {
      arr.push(e.target.value);
      setTimesId([...timesId, ...arr]);
    } else {
      arr = timesId.filter((item) => item !== e.target.value);
      setTimesId([...arr]);
    }
  };

  const handleDelete = (index) => {
    setPickedStudents(pickedStudents.filter((item) => item.id !== index));
    setStudentIds(studentIds.filter((item) => item !== index));
  };

  const handleSubmit = async () => {
    if (!timesId) {
      setErrorString("გთხოვთ აირჩიოთ ლექციის დრო ");
      return;
    }
    setIsSubmitting(true);
    const fd = new FormData();
    fd.append("syllabus_id", syllabusId);
    studentIds.forEach((item, index) => {
      fd.append(`student_ids[${index}]`, item);
    });
    timesId.forEach((item, index) => {
      fd.append(`curriculum_lecture_time_ids[${index}]`, item);
    });
    try {
      const response = await apiClientProtected().post(
        "/administration/set-students-in-syllabus",
        fd
      );
      setIsSubmitting(false);
      setOpenModal(false);
      setAlertMessage({ isOpen: true, title: response.data.message });
      const dataResponse = await apiClientProtected().get(url);
      setData(dataResponse.data);
      setSignsDataUpdate(!signsDataUpdate);
      console.log("reise reise seeman reise");
    } catch (err) {
      setIsSubmitting(false);
      console.log(err);
      setAlertMessage({
        isOpen: true,
        title: err.response.data.message,
        status: "error",
      });
      setErrors(err.response.data);
    }
  };

  return (
    <div className="right__side">
      <div className="input__groups pre-requisite-class position-relative">
        <RadioList>
          {lectureTimes.map((item, index) => (
            <li key={index}>
              <div className="d-flex gap-8">
                <input
                  type="checkbox"
                  className="pointer"
                  name={item.id}
                  value={item.id}
                  onChange={handleTimes}
                />
                <span>
                  {WEEK_DAYS.find((week) => item.week_day === week.id).name}
                </span>
                <span>
                  {item.start_time} - {item.end_time}
                </span>
                <span>{item.auditorium.name}</span>
                <span>
                  {item.lecturer.first_name} {item.lecturer.last_name}
                </span>
              </div>
            </li>
          ))}
        </RadioList>
        {errorString && <div className="text-danger mb-4">{errorString}</div>}
        <span className="position-relative">
          <input
            type="text"
            className="form-control"
            name="students"
            placeholder={locale && langs[locale]["search"]}
            value={searchString}
            onChange={(e) => setSearchString(e.target.value)}
          />
          {isLoading && (
            <span
              className="position-absolute"
              style={{ top: "25%", right: "3%" }}
            >
              <span className="loader"></span>
            </span>
          )}
        </span>
        {
          <ul
            className={`pre-dropdown ${searchString.length > 0 && "d-block"}`}
          >
            {students.map((item) => (
              <SearchItem
                key={item.id}
                onClick={() => handlePick(item)}
                className="search-item"
              >
                <span>
                  {item.name} {item.surname}
                </span>
                <div className="student-details">
                  <span>
                    {locale && langs[locale]["personal_id"]}: {item.personal_id}
                  </span>
                  <span>
                    {locale && langs[locale]["course"]}: {item.course}
                  </span>
                  <span>
                    {locale && langs[locale]["program"]}: {item.program.name_ka}
                  </span>
                </div>
              </SearchItem>
            ))}
          </ul>
        }
      </div>
      <ul>
        {pickedStudents &&
          pickedStudents.map((item, index) => (
            <Item key={item.id} className="my-4 position-relative">
              <div className="user-info">
                <div className="image-wrapper">
                  <img src="/assets/media/avatars/blank.png" alt="" />
                </div>
                <div className="user-details">
                  <h5>{item.name + " " + item.surname}</h5>
                  <div className="user-meta">
                    <span>
                      <strong>{locale && langs[locale]["personal_id"]}:</strong>{" "}
                      {item.personal_id}
                    </span>
                    <span>
                      <strong>{locale && langs[locale]["program"]}:</strong>{" "}
                      {item.program.name_ka}
                    </span>
                  </div>
                </div>
              </div>
              <div onClick={() => handleDelete(item.id)} className="pointer">
                <MdOutlineDelete size={18} />
              </div>
            </Item>
          ))}
      </ul>
      {errors && <div className="text-danger">{errors["message"]}</div>}
      {pickedStudents.length ? (
        <div className="d-flex align-items-center justify-content-center mt-4">
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            {locale && langs[locale]["close"]}
          </button>
          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {locale && langs[locale]["add"]}
            </button>
          )}
        </div>
      ) : null}
    </div>
  );
};

export default AddStudentForm;

const Item = styled.li`
  border: 1px solid #ddd;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.15);
  .image-wrapper {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
    }
  }

  .user-info {
    display: flex;
    gap: 8px;
  }

  .user-details {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .user-meta {
    display: flex;
    gap: 1rem;
    margin-top: 4px;

    span {
      font-size: 12px;
    }
  }
`;

const SearchItem = styled.li`
  display: flex;
  justify-content: space-between;
  align-items: center;
  .student-details {
    display: flex;
    gap: 1rem;
  }
`;

const RadioList = styled.ul`
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ccc;
  margin-bottom: 0.5rem;

  li {
    display: flex;
    gap: 8px;
    padding: 1rem;
    border: none;
    border-bottom: 1px solid #ccc;
    &:last-child {
      border-bottom: none;
    }
  }
`;
