import { useState, useEffect } from "react";
import { FormElement, FormItem, FlexElement } from "./../styles";
import { useTableContext } from "./../../context/TableContext";
import SubmitLoader from "./../../ui/SubmitLoader";
import apiClientProtected from "../../../helpers/apiClient";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

import SweetAlert2 from "react-sweetalert2";

const AuditoriumEditForm = ({ fetchLink, id, setOpenModal, data }) => {
  const { locale } = useLocaleContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userAdded, setUserAdded] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [fieldData, setFieldData] = useState({
    name: "",
    quantity: "",
    campus_id: "",
    student_aid: "0",
    projector: "0",
    multimedia: "0",
    exam_audience: "0",
    cameras: "0",
    computer_lab: "0",
  });

  useEffect(() => {
    setFieldData(data);
  }, []);

  const { errors, setErrors, relationFields, handleDataEdit } =
    useTableContext();

  const handleChange = (e) => {
    if (e.target.type === "checkbox") {
      if (e.target.checked) {
        setFieldData({ ...fieldData, [e.target.name]: "1" });
      } else {
        setFieldData({ ...fieldData, [e.target.name]: "0" });
      }
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log(fieldData);
    setIsSubmitting(true);

    const fd = new FormData();
    fd.append("_method", "PUT");

    for (let key in fieldData) {
      // if(fieldData[key] !== '0') {
      //   fd.append(key, fieldData[key])
      // }
      fd.append(key, fieldData[key]);
    }

    try {
      const response = await apiClientProtected().post(
        `${fetchLink}/${id}`,
        fd
      );
      setErrors(null);
      setIsSubmitting(false);
      handleDataEdit(response.data);
      setUserAdded(true);
      setOpenModal(false);
      console.log(response);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "წარმატებით შეიცვალა",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
    } catch (err) {
      setErrors(err.response.data);
      setIsSubmitting(false);
    }
  };

  return (
    <FormElement onSubmit={handleSubmit}>
      <FormItem>
        <h6
          style={{
            borderBottom: "1px solid #eee",
            paddingBottom: "0.75rem",
            marginBottom: "1rem",
          }}
        >
          {locale && langs[locale]["main_info"]}
        </h6>
        <FlexElement>
          <div>
            <label htmlFor="name">{locale && langs[locale]["title"]}</label>
            <input
              type="text"
              name="name"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["title"]}
              id="name"
              value={fieldData.name}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name}</div>}
          </div>

          <div>
            <label htmlFor="quantity">
              {locale && langs[locale]["students_quantity"]}
            </label>
            <input
              type="text"
              name="quantity"
              className="form-control mb-3 form-control form-control-solid "
              placeholder={locale && langs[locale]["students_quantity"]}
              id="quantity"
              value={fieldData.quantity}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.quantity}</div>}
          </div>
        </FlexElement>
        <FlexElement>
          <div>
            <label htmlFor="campus_id">
              {locale && langs[locale]["campuses"]}
            </label>
            <select
              name="campus_id"
              className="form-control mb-3 form-control form-control-solid "
              id="campus_id"
              value={fieldData.campus_id}
              onChange={handleChange}
            >
              <option key="12346587asdqwe" value="">
                {locale && langs[locale]["choose_item"]}
              </option>
              {relationFields &&
                relationFields["campuses"]?.options &&
                Object.keys(relationFields["campuses"]?.options).map(
                  (field) => (
                    <option key={field} value={field}>
                      {relationFields["campuses"].options[field]}
                    </option>
                  )
                )}
            </select>
            {errors && <div className="text-danger">{errors.campus_id}</div>}
          </div>
        </FlexElement>
      </FormItem>

      <FormItem>
        <h6
          style={{
            borderBottom: "1px solid #eee",
            paddingBottom: "0.75rem",
            marginBottom: "1rem",
          }}
        >
          {locale && langs[locale]["additional_information"]}
        </h6>
        <FlexElement>
          <div>
            <label htmlFor="student_aid" style={{ display: "block" }}>
              {locale && langs[locale]["disabled_persons"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                checked={
                  fieldData.student_aid === 1 || fieldData.student_aid === "1"
                }
                id="student_aid"
                name="student_aid"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.student_aid === "0" || !fieldData.student_aid
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="projector" style={{ display: "block" }}>
              {locale && langs[locale]["projector"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                checked={
                  fieldData.projector === 1 || fieldData.projector === "1"
                }
                id="projector"
                name="projector"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.projector === "0" || !fieldData.projector
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="multimedia" style={{ display: "block" }}>
              {locale && langs[locale]["multimedia"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                checked={
                  fieldData.multimedia === 1 || fieldData.multimedia === "1"
                }
                id="multimedia"
                name="multimedia"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.multimedia === "0" || !fieldData.multimedia
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
        </FlexElement>

        <FlexElement>
          <div>
            <label htmlFor="exam_audience" style={{ display: "block" }}>
              {locale && langs[locale]["exam_room"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                checked={
                  fieldData.exam_audience === 1 ||
                  fieldData.exam_audience === "1"
                }
                id="exam_audience"
                name="exam_audience"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.exam_audience === "0" || !fieldData.exam_audience
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
          <div>
            <label htmlFor="cameras" style={{ display: "block" }}>
              {locale && langs[locale]["cctv"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                checked={fieldData.cameras === 1 || fieldData.cameras === "1"}
                id="cameras"
                name="cameras"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.cameras === "0" || !fieldData.cameras
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>

          <div>
            <label htmlFor="computer_lab" style={{ display: "block" }}>
              {locale && langs[locale]["computer_lab"]}
            </label>
            <div className="form-check form-switch form-check-custom form-check-solid">
              <input
                className="form-check-input"
                type="checkbox"
                checked={
                  fieldData.computer_lab === 1 || fieldData.computer_lab === "1"
                }
                id="computer_lab"
                name="computer_lab"
                onChange={handleChange}
              />
              <span className="form-check-label fw-bold text-muted">
                {fieldData.computer_lab === "0" || !fieldData.computer_lab
                  ? locale && langs[locale]["no"]
                  : locale && langs[locale]["yes"]}
              </span>
            </div>
          </div>
        </FlexElement>
      </FormItem>

      <div className="d-flex align-items-center justify-content-center mt-4">
        <button
          className="btn btn-light-primary me-3"
          onClick={(e) => {
            e.preventDefault();
            setOpenModal(false);
          }}
        >
          {locale && langs[locale]["close"]}
        </button>

        {isSubmitting ? (
          <SubmitLoader type="primary" margin="mt-0" />
        ) : (
          <button
            className="btn btn-primary"
            type="submit"
            disabled={isSubmitting}
          >
            {locale && langs[locale]["edit"]}
          </button>
        )}
      </div>
      {userAdded && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </FormElement>
  );
};

export default AuditoriumEditForm;
