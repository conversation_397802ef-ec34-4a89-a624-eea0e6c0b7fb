import { useState, useEffect } from "react";
import { useTableContext } from "../../context/TableContext";
import {
  FormElement,
  FormItem,
  FlexElement,
  ButtonController,
} from "./../styles";
import DatePicker from "react-datepicker";
import { dateFormat, timeFormat } from "../../../helpers/funcs";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "./../../ui/SubmitLoader";
import SweetAlert2 from "react-sweetalert2";
import setHours from "date-fns/setHours";
import setMinutes from "date-fns/setMinutes";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

const CalendarForm = ({ setOpenModal, eventData }) => {
  const { locale } = useLocaleContext();
  const [success, setSuccesss] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldData, setFieldData] = useState({
    title: "",
    description: "",
    start: "",
    end: "",
  });

  // useEffect(() => {
  //   // setFieldData({...fieldData, event_date })
  // }, [])

  const { errors, setErrors, handleDataSubmit } = useTableContext();

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();

    for (let key in fieldData) {
      if (key === "start" || key === "end") {
        console.log(dateFormat(fieldData[key], "time", "-"));
        fd.append(key, dateFormat(fieldData[key], "time", "-"));
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    try {
      const response = await apiClientProtected().post("/events", fd);
      console.log(response);
      handleDataSubmit(response.data);
      setSuccesss(true);
      setErrors(null);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      // setOpenModal(false)
    } catch (err) {
      setErrors(err.response.data.errors);
      setIsSubmitting(false);
    }
  };
  return (
    <div>
      <FormElement onSubmit={handleSubmit}>
        <FormItem>
          <FlexElement>
            <div>
              <label htmlFor="title">სათაური</label>
              <input
                type="text"
                name="title"
                className="form-control mb-3 form-control form-control-solid "
                placeholder="სახელი"
                id="title"
                value={fieldData.title}
                onChange={handleChange}
              />
              {errors && <div className="text-danger">{errors.title}</div>}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="start">დაწყების დრო</label>
              <DatePicker
                className="form-control mb-3 form-control form-control-solid"
                placeholderText="dd/mm/yyyy HH:mm"
                selected={fieldData.start}
                onChange={(date) => handleDate(date, "start")}
                showTimeSelect
                timeFormat="HH:mm"
                timeIntervals={15}
                timeCaption="Time"
                minTime={setHours(setMinutes(new Date(), 0), 8)}
                maxTime={setHours(setMinutes(new Date(), 45), 23)}
                dateFormat="dd/MM/yyyy hh:mm"
              />
              {errors && <div className="text-danger">{errors.start}</div>}
            </div>
            <div>
              <label htmlFor="end">დასრულების დრო</label>
              <DatePicker
                className="form-control mb-3 form-control form-control-solid"
                placeholderText="dd/mm/yyyy HH:mm"
                selected={fieldData.end}
                onChange={(date) => handleDate(date, "end")}
                showTimeSelect
                timeFormat="HH:mm"
                timeIntervals={15}
                timeCaption="Time"
                minTime={setHours(setMinutes(new Date(), 0), 8)}
                maxTime={setHours(setMinutes(new Date(), 45), 23)}
                dateFormat="dd/MM/yyyy HH:mm"
              />
              {errors && <div className="text-danger">{errors.end}</div>}
            </div>
          </FlexElement>
          <FlexElement>
            <div>
              <label htmlFor="description">აღწერა</label>
              <input
                type="text"
                name="description"
                className="form-control mb-3 form-control form-control-solid "
                placeholder="აღწერა"
                id="description"
                value={fieldData.description}
                onChange={handleChange}
              />
              {errors && (
                <div className="text-danger">{errors.description}</div>
              )}
            </div>
          </FlexElement>
        </FormItem>
        <ButtonController>
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            დახურვა
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              დამატება
            </button>
          )}
        </ButtonController>
        {success && (
          <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
        )}
      </FormElement>
    </div>
  );
};

export default CalendarForm;
