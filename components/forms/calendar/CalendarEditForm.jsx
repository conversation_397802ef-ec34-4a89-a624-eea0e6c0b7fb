import { useState, useEffect } from "react";
import { useTableContext } from "../../context/TableContext";
import {
  FormElement,
  FormItem,
  FlexElement,
  ButtonController,
} from "./../styles";
import DatePicker from "react-datepicker";
import { timeFormat, dateFormat } from "../../../helpers/funcs";
import apiClientProtected from "../../../helpers/apiClient";
import SubmitLoader from "./../../ui/SubmitLoader";
import SweetAlert2 from "react-sweetalert2";
import BaseFilterSelect from "./../../base/BaseFilterSelect";
import setHours from "date-fns/setHours";
import setMinutes from "date-fns/setMinutes";
import { useLocaleContext } from "./../../context/LocaleContext";
import { langs } from "./../../locale";

const CalendarEditForm = ({ setOpenModal, eventData }) => {
  const { locale } = useLocaleContext();
  const [success, setSuccesss] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lecturers, setLecturers] = useState([]);
  const [auditoriums, setAuditoriums] = useState([]);
  const [campuses, setCampuses] = useState([]);
  const [fieldData, setFieldData] = useState({
    title: "",
    description: "",
    start: "",
    end: "",
  });

  const { errors, setErrors, handleDataEdit } = useTableContext();

  useEffect(() => {
    const getData = async () => {
      const response = await apiClientProtected().get("/lecturers");
      const lecturers = response.data.lecturers.data.map((item) => {
        item.label = item.first_name + " " + item.last_name;
        return item;
      });
      setLecturers(lecturers);
      const responseCampuses = await apiClientProtected().get("/campuses");
      setCampuses(responseCampuses.data.campuses.data);
      console.log(responseCampuses.data.data, "kampusebi");
      const responseAuditoriums = await apiClientProtected().get(
        "/auditoriums"
      );
      setAuditoriums(responseAuditoriums.data.auditoriums.data);
      console.log(responseAuditoriums.data);
    };

    getData();

    if (eventData.type !== "lecture") {
      const start = new Date(eventData.start);
      const end = new Date(eventData.end);
      setFieldData({ ...eventData, start, end });
    } else {
      const start = new Date(eventData.start);
      const end = new Date(eventData.end);
      setFieldData({
        id: eventData.id,
        start,
        end,
        auditorium_id: eventData.auditorium.id,
        campus_id: eventData.campus.id,
        lecturer_id: eventData.lecturer.id,
      });
    }
  }, []);

  const handleChange = async (e) => {
    if (e.target.name === "campus_id") {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
      const response = await apiClientProtected().get(
        `/auditoriums?campus_id=${e.target.value}`
      );
      setAuditoriums(response.data.auditoriums.data);
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleDate = (date, name) => {
    setFieldData({ ...fieldData, [name]: date });
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    setFieldData({ ...fieldData, [name]: arrData });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    const fd = new FormData();
    fd.append("_method", "PUT");
    console.log(fieldData);

    for (let key in fieldData) {
      if (key === "start" || key === "end") {
        fd.append(key, dateFormat(fieldData[key], "time", "-"));
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    const endPoint =
      eventData.type === "lecture" ? "administration/calendar" : "events";

    try {
      const response = await apiClientProtected().post(
        `/${endPoint}/${eventData.id}`,
        fd
      );
      console.log(response);
      handleDataEdit(response.data, response.data.type);
      setSuccesss(true);
      setErrors(null);
      setIsSubmitting(false);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "თქვენი ღონისძიება წარმატებით შეიცვალა",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      // setOpenModal(false)
    } catch (err) {
      setErrors(err.response.data.errors);
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <FormElement onSubmit={handleSubmit}>
        {eventData.type !== "lecture" ? (
          <FormItem>
            <FlexElement>
              <div>
                <label htmlFor="title">სათაური</label>
                <input
                  type="text"
                  name="title"
                  className="form-control mb-3 form-control form-control-solid "
                  placeholder="სათაური"
                  id="title"
                  value={fieldData.title}
                  onChange={handleChange}
                />
                {errors && <div className="text-danger">{errors.title}</div>}
              </div>
            </FlexElement>
            <FlexElement>
              <div>
                <label htmlFor="start">დაწყების დრო</label>
                <DatePicker
                  className="form-control mb-3 form-control form-control-solid"
                  placeholderText="HH:mm"
                  selected={fieldData.start}
                  onChange={(date) => handleDate(date, "start")}
                  showTimeSelect
                  timeFormat="HH:mm"
                  timeIntervals={15}
                  timeCaption="Time"
                  minTime={setHours(setMinutes(new Date(), 0), 8)}
                  maxTime={setHours(setMinutes(new Date(), 45), 23)}
                  dateFormat="dd/MM/yyyy HH:mm"
                />
                {errors && <div className="text-danger">{errors.start}</div>}
              </div>
              <div>
                <label htmlFor="end">დასრულების დრო</label>
                <DatePicker
                  className="form-control mb-3 form-control form-control-solid"
                  placeholderText="HH:mm"
                  selected={fieldData.end}
                  onChange={(date) => handleDate(date, "end")}
                  showTimeSelect
                  timeFormat="HH:mm"
                  timeIntervals={15}
                  timeCaption="Time"
                  minTime={setHours(setMinutes(new Date(), 0), 8)}
                  maxTime={setHours(setMinutes(new Date(), 45), 23)}
                  dateFormat="dd/MM/yyyy HH:mm"
                />
                {errors && <div className="text-danger">{errors.end}</div>}
              </div>
            </FlexElement>
            <FlexElement>
              <div>
                <label htmlFor="description">აღწერა</label>
                <input
                  type="text"
                  name="description"
                  className="form-control mb-3 form-control form-control-solid "
                  placeholder="აღწერა"
                  id="description"
                  value={fieldData.description}
                  onChange={handleChange}
                />
                {errors && (
                  <div className="text-danger">{errors.description}</div>
                )}
              </div>
            </FlexElement>
          </FormItem>
        ) : (
          <FormItem>
            <FlexElement>
              <div>
                <label htmlFor="lecturer_id">ლექტორი</label>
                <BaseFilterSelect
                  data={lecturers}
                  name="lecturer_id"
                  setValue={handleFilterValue}
                  searchable={true}
                  multiSelect={false}
                  selectStyles={true}
                  placeholder="ლექტორის არჩევა"
                  defaultValue={fieldData.lecturer_id}
                />
                {errors && (
                  <div className="text-danger">{errors.lecturer_id}</div>
                )}
              </div>
              <div>
                <label htmlFor="campus_id">კამპუსი</label>
                <select
                  name="campus_id"
                  className="form-control mb-3 form-control form-control-solid "
                  id="campus_id"
                  value={fieldData.campus_id}
                  onChange={handleChange}
                >
                  <option value="" key="321546asd">
                    არჩევა
                  </option>
                  {campuses?.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name_ka}
                    </option>
                  ))}
                </select>
                {errors && (
                  <div className="text-danger">{errors.campus_id}</div>
                )}
              </div>
              <div>
                <label htmlFor="auditorium_id">აუდიტორია</label>
                <select
                  name="auditorium_id"
                  className="form-control mb-3 form-control form-control-solid "
                  id="auditorium_id"
                  value={fieldData.auditorium_id}
                  onChange={handleChange}
                >
                  <option value="" key="321546asd">
                    არჩევა
                  </option>
                  {auditoriums.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name}
                    </option>
                  ))}
                </select>
                {errors && (
                  <div className="text-danger">{errors.auditorium_id}</div>
                )}
              </div>
            </FlexElement>
            <FlexElement>
              <div>
                <label htmlFor="start">დაწყების დრო</label>
                <DatePicker
                  className="form-control mb-3 form-control form-control-solid"
                  placeholderText="HH:mm"
                  selected={fieldData.start}
                  onChange={(date) => handleDate(date, "start")}
                  showTimeSelect
                  timeFormat="HH:mm"
                  timeIntervals={15}
                  timeCaption="Time"
                  minTime={setHours(setMinutes(new Date(), 0), 8)}
                  maxTime={setHours(setMinutes(new Date(), 45), 23)}
                  dateFormat="dd/MM/yyyy HH:mm"
                />
                {errors && <div className="text-danger">{errors.start}</div>}
              </div>
              <div>
                <label htmlFor="end">დასრულების დრო</label>
                <DatePicker
                  className="form-control mb-3 form-control form-control-solid"
                  placeholderText="HH:mm"
                  selected={fieldData.end}
                  onChange={(date) => handleDate(date, "end")}
                  showTimeSelect
                  timeFormat="HH:mm"
                  timeIntervals={15}
                  timeCaption="Time"
                  minTime={setHours(setMinutes(new Date(), 0), 8)}
                  maxTime={setHours(setMinutes(new Date(), 45), 23)}
                  dateFormat="dd/MM/yyyy HH:mm"
                />
                {errors && <div className="text-danger">{errors.end}</div>}
              </div>
            </FlexElement>
          </FormItem>
        )}
        <ButtonController>
          <button
            className="btn btn-light-primary me-3"
            onClick={(e) => {
              e.preventDefault();
              setOpenModal(false);
            }}
          >
            დახურვა
          </button>

          {isSubmitting ? (
            <SubmitLoader type="primary" margin="mt-0" />
          ) : (
            <button
              className="btn btn-primary"
              type="submit"
              disabled={isSubmitting}
            >
              რედაქტირება
            </button>
          )}
        </ButtonController>
        {success && (
          <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
        )}
      </FormElement>
    </div>
  );
};

export default CalendarEditForm;
