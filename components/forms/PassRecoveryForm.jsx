import { useState } from "react";
import apiClientProtected from "../../helpers/apiClient";
import { useTableContext } from "./../context/TableContext";
import { useLocaleContext } from "../context/LocaleContext";
import { langs } from "../locale";
import SweetAlert2 from "react-sweetalert2";

function PassRecoveryForm({ handleModalClose, id }) {
  const { locale } = useLocaleContext();
  const { errors, setErrors, setOpenModal } = useTableContext();
  const [password, setPassword] = useState("");
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});

  const handleSubmit = async (e) => {
    console.log(e);
    e.preventDefault();

    const recoveryObject = {
      id,
      password,
    };

    try {
      const response = await apiClientProtected().post(
        process.env.NEXT_PUBLIC_PASS_RECOVERY,
        recoveryObject
      );

      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["create_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
      });
      handleModalClose();
      setOpenModal(false);
    } catch (err) {
      setErrors(err.response.data.errors);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="py-4">
      <div>
        <input
          className="form-control mb-3 form-control form-control-solid"
          onChange={(e) => setPassword(e.target.value)}
          value={password}
          placeholder={locale && langs[locale]["enter_new_password"]}
        />
        {errors && (
          <div className="text-danger" style={{ textAlign: "left" }}>
            {errors.password}
          </div>
        )}
      </div>

      <div className="d-flex align-items-center justify-content-center mt-4">
        <button
          className="btn btn-light-primary me-3"
          onClick={(e) => {
            e.preventDefault();
            handleModalClose();
          }}
        >
          {locale && langs[locale]["close"]}
        </button>
        <button className="btn btn-primary" type="submit">
          {locale && langs[locale]["save"]}
        </button>
      </div>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
      )}
    </form>
  );
}

export default PassRecoveryForm;
