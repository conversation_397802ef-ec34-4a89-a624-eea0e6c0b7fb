import apiClientProtected, { apiClient } from "../../helpers/apiClient";
import { langs } from "../locale";
import { useState, useEffect } from "react";
import { MdClose, MdOutlineDelete, MdCheck } from "react-icons/md";
import ModalWrapper from "../modal/ModalWrapper";
import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import SweetAlert2 from "react-sweetalert2";
import ButtonLoader from "../ui/ButtonLoader";
import { selectsArray } from "./formsArray";
import { useRouter } from "next/router";
import {
  StyledContainer,
  StyledFormGroup,
  StyledFormTable,
  StyledTitle,
  TableCellCheck,
} from "./styles";

import {
  weekDays,
  hoursRange,
  academicHonesty,
  examRules,
  assessingSystem,
  preRequsitesData,
} from "./silabusData";

const CreateHse = ({ learn_year_id, syllabusId, type }) => {
  const { locale } = useLocaleContext();
  const { errors, setErrors } = useTableContext();
  const router = useRouter();
  const [lecturersSearch, setLecturersSearch] = useState("");
  const [isLecturersLoading, setIsLecturersLoading] = useState(false);
  const [lecturersOptions, setLecturersOptions] = useState([]);
  const [editExam, setEditExam] = useState({});
  const [openModal, setOpenModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [sylabusStatus, setSylabusStatus] = useState([]);
  const [fieldData, setFieldData] = useState({
    name: "",
    credits: "",
    lecture_hours: "",
    independent_work_hours: "",
    contact_hours: "",
    code: "",
    semester_id: "",
    status_id: "",
    lecturers: [],
    exams: [],
  });

  useEffect(() => {
    if (lecturersSearch) {
      handleLecturersFilter();
    }
  }, [lecturersSearch]);

  useEffect(() => {
    if (type === "edit") {
      (async () => {
        const response = await apiClientProtected().get(
          `/syllabus-profession/${syllabusId}`
        );
        console.log(response);
        setFieldData({
          ...response.data.syllabus,
          exams: [],
          exams: response.data.syllabus.assignments.map((item) => {
            return {
              ...item,
              name_ka: item.assessment_component.name_ka,
              name_en: item.assessment_component.name_en,
              id: item.assessment_component_id,
            };
          }),
          lecturers: response.data.syllabus.lecturer_contact_times.map(
            (item) => {
              return { ...item.lecturer, ...item };
            }
          ),
        });
      })();
    }
  }, [type]);

  useEffect(() => {
    (async () => {
      const response = await apiClientProtected().get(`/syllabi/create`);

      const status = Object.entries(response.data.statuses).map(
        (item, index) => {
          return { id: item[0], title: item[1], code: index };
        }
      );

      setSylabusStatus(status);
    })();
  }, []);

  const handleChange = (e) => {
    setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    console.log(learn_year_id);
  };

  const handleLecturersFilter = async () => {
    setIsLecturersLoading(true);
    const response = await apiClientProtected().get(
      `/administration/syllabus-lecturer/${lecturersSearch}`
    );
    console.log(response);
    setLecturersOptions(response.data);
    setIsLecturersLoading(false);

    console.log(response);
  };

  const addLecturer = (data) => {
    if (!fieldData.lecturers.filter((item) => item.id === data.id).length) {
      const arr = [...fieldData.lecturers];
      const lecturerItem = {
        ...data,
        end_time: "",
        start_time: "",
        week_day: "",
        lecturer_id: data.id,
      };
      console.log(data, arr);
      setFieldData({
        ...fieldData,
        lecturers: [...fieldData.lecturers, lecturerItem],
      });
    }
    setLecturersOptions([]);
    setLecturersSearch("");
  };

  const handleLecturers = (e, id) => {
    const newArray = fieldData.lecturers.map((item) => {
      if (id == item.id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setFieldData({
      ...fieldData,
      lecturers: newArray,
    });
  };

  const deleteLecturer = (index) => {
    let data = [...fieldData.lecturers];

    data = data.filter((item, i) => i !== index);
    setFieldData({ ...fieldData, lecturers: data });
  };

  const handleModalShow = () => {
    return true;
  };

  const handleModalClose = () => {
    return false;
  };

  const handleRate = (data) => {
    console.log(data);

    const mergeId = { ...data };
    setFieldData({ ...fieldData, exams: [...fieldData.exams, mergeId] });
  };

  const handleExamData = (e, id) => {
    const exams = fieldData.exams.map((item) => {
      if (item.id === id) {
        item[e.target.name] = e.target.value;
      }
      return item;
    });
    setFieldData({ ...fieldData, exams });
  };

  const handleExamDelete = (id) => {
    const data = [...fieldData.exams];
    const exams = data.filter((item) => item.id !== id);
    setFieldData({ ...fieldData, exams });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsLoading(true);

    const fd = new FormData();
    for (let key in fieldData) {
      if (key === "lecturers") {
        for (let i = 0; i < fieldData.lecturers.length; i++) {
          const arr = [];
          for (let arr_key in fieldData.lecturers[i]) {
            arr.push(fieldData.lecturers[i][key]);
            fd.append(
              `lecturers[${i}][${arr_key}]`,
              fieldData.lecturers[i][arr_key]
            );
          }
        }
      } else if (key === "exams") {
        for (let i = 0; i < fieldData.exams.length; i++) {
          fd.append(`exams[${i}][id]`, fieldData.exams[i].id);
        }
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    fd.append("total_hours", fieldData.lecture_hours);
    if (type !== "edit") {
      fd.append("learn_year_id", learn_year_id);
    }
    fd.append("academic_degree_id", 4);
    fd.append("is_profession", 1);
    fd.append("syllabus_type_id", 2);

    try {
      const response =
        type === "edit"
          ? await apiClientProtected().post(
              `/syllabus-profession/update/${syllabusId}`,
              fd
            )
          : await apiClientProtected().post(`/syllabus-profession`, fd);
      setIsLoading(false);
      router.push(
        `/admin/curriculum?schools=${response.data.learn_year.program.school_id}&programs=${response.data.learn_year.program.id}&flows=${fieldData.learn_year_id}`
      );
      console.log(response);
    } catch (err) {
      setIsLoading(false);
      setErrors(err.response.data.errors);
      console.log(err);
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>{locale && langs[locale]["silabus_title"]}</StyledTitle>
      <form onSubmit={handleSubmit}>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["name_of_tr_course"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="name"
              value={fieldData.name}
              placeholder={locale && langs[locale]["name_of_tr_course"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.name}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["ects_credits"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="credits"
              value={fieldData.credits}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.credits}</div>}
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">{locale && langs[locale]["code"]}</span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="code"
              value={fieldData.code}
              placeholder={locale && langs[locale]["code"]}
              onChange={handleChange}
            />
            {errors && <div className="text-danger">{errors.code}</div>}
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["semester_of_course_impl"]}
            </span>
          </div>
          <div className="right__side">
            <select
              className="form-select"
              onChange={handleChange}
              value={fieldData.semester_id}
              name="semester_id"
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {selectsArray[0].options.slice(0, 4).map((option) => (
                <option key={option.value} value={option.id}>
                  {option.value}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.semester_id}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["course_status"]}
            </span>
          </div>
          <div className="right__side">
            {/* <input
              className="form-control"
              value={sylabus.status_id.title}
              name="status_id"
              placeholder="კურსის დასახელება"
              onChange={handleChange} /> */}
            <select
              name="status_id"
              className="form-select"
              value={fieldData.status_id}
              onChange={handleChange}
            >
              <option value="">{locale && langs[locale]["choose_item"]}</option>
              {sylabusStatus.map((item) => (
                <option key={item.id} value={item.id}>
                  {item.title}
                </option>
              ))}
            </select>
            {errors && <div className="text-danger">{errors.status_id}</div>}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["free_hours"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="independent_work_hours"
              value={fieldData.independent_work_hours}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.independent_work_hours}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["lecture_hours"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="lecture_hours"
              value={fieldData.lecture_hours}
              placeholder={locale && langs[locale]["quantity"]}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.lecture_hours}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["contact_hours"]}
            </span>
          </div>
          <div className="right__side">
            <input
              className="form-control"
              name="contact_hours"
              value={fieldData.contact_hours}
              placeholder={locale && langs[locale]["contact_hours"]}
              onChange={handleChange}
            />
            {errors && (
              <div className="text-danger">{errors.contact_hours}</div>
            )}
          </div>
        </StyledFormGroup>

        <StyledFormGroup>
          <div className="left__side">
            <span className="text-bold">
              {locale && langs[locale]["lecturer"]}
            </span>
          </div>
          <div className="right__side">
            <div className="input__groups pre-requisite-class position-relative">
              <span className="position-relative">
                <input
                  type="text"
                  className="form-control"
                  name="lecturers"
                  placeholder={locale && langs[locale]["search"]}
                  value={lecturersSearch}
                  onChange={(e) => setLecturersSearch(e.target.value)}
                />
                {isLecturersLoading && (
                  <span
                    className="position-absolute"
                    style={{ top: "25%", right: "3%" }}
                  >
                    <span className="loader"></span>
                  </span>
                )}
              </span>
              {
                <ul
                  className={`pre-dropdown ${
                    lecturersSearch.length > 0 && "d-block"
                  }`}
                >
                  {lecturersOptions.map((item) => (
                    <li key={item.id} onClick={() => addLecturer(item)}>
                      {item.first_name} {item.last_name}
                    </li>
                  ))}
                </ul>
              }
              {errors && <div className="text-danger">{errors.lecturers}</div>}
            </div>
            <ul>
              {fieldData.lecturers &&
                fieldData.lecturers.map((item, index) => (
                  <li key={item.id} className="my-4 position-relative">
                    <h5 className="mb-2">
                      {item.first_name + " " + item.last_name}
                    </h5>
                    <div className="mb-2">{item.email}</div>
                    <div className="mb-2">{item.phone}</div>
                    <div className="d-flex gap-4">
                      <div className="flex-grow-1">
                        <select
                          className="form-select"
                          name="week_day"
                          value={item.week_day}
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          <option value="">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {weekDays.map((item, index) => (
                            <option key={index} value={item.id}>
                              {locale && langs[locale][item.name]}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.week_day`]}
                          </div>
                        )}
                      </div>
                      <div className="flex-grow-1">
                        <select
                          className="form-select"
                          name="start_time"
                          value={item.start_time}
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          <option value="">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {hoursRange.map((item, index) => (
                            <option key={index} value={item}>
                              {item}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.start_time`]}
                          </div>
                        )}
                      </div>
                      <div className="flex-grow-1">
                        <select
                          className="form-select"
                          name="end_time"
                          value={item.end_time}
                          onChange={(e) => handleLecturers(e, item.id)}
                        >
                          <option value="">
                            {locale && langs[locale]["choose_item"]}
                          </option>
                          {hoursRange.map((item, index) => (
                            <option key={index} value={item}>
                              {item}
                            </option>
                          ))}
                        </select>
                        {errors && (
                          <div className="text-danger">
                            {errors[`lecturers.${index}.end_time`]}
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      onClick={() => deleteLecturer(index)}
                      className="position-absolute top-0 end-0 pointer"
                    >
                      <MdOutlineDelete size={18} />
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div
            className="left__side"
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <span className="text-bold">
              {locale && langs[locale]["Int_and_final_eval"]}
            </span>
            <ModalWrapper
              handleModalShow={handleModalShow}
              handleModalClose={handleModalClose}
              handleRate={handleRate}
              editExam={editExam}
              examsLength={fieldData.exams.length}
              parentArray={fieldData.exams}
              setOpenModal={setOpenModal}
              openModal={openModal}
              title="შეფასების დამატება"
              modalType="hse"
            ></ModalWrapper>
          </div>
          <div className="right__side">
            {fieldData.exams.length > 0 && (
              <div>
                <table className="w-100 border bg-white">
                  <thead>
                    <tr>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["title"]}
                      </th>
                      <th className="border p-2" style={{ width: "10%" }}>
                        {locale && langs[locale]["action"]}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {fieldData.exams.map((item, index) => (
                      <tr key={item.id}>
                        <td className="border p-2">{item[`name_${locale}`]}</td>

                        <td className="border p-2">
                          <button
                            className="btn btn-danger"
                            type="button"
                            onClick={() => handleExamDelete(item.id)}
                          >
                            -
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {errors && (
                  <div className="text-danger">
                    {errors["exams.0.description"]}
                  </div>
                )}
              </div>
            )}
            {errors && <div className="text-danger">{errors.exams}</div>}
          </div>
        </StyledFormGroup>
        <StyledFormGroup>
          <div className="left__side"></div>
          <div className="right__side">
            <button className="btn btn-primary btn-height" type="submit">
              {isLoading ? (
                <ButtonLoader />
              ) : type === "edit" ? (
                "რედაქტირება"
              ) : (
                "შექმნა"
              )}
            </button>
          </div>
        </StyledFormGroup>
      </form>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </StyledContainer>
  );
};

export default CreateHse;
