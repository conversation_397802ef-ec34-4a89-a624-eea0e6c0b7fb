import styled from "styled-components";
import Title from "./Title";
import { edit } from "./svg";
import Select from "./Select";
import { dropdown } from "./svg";
import { useState } from "react";
import { useTableContext } from "../context/TableContext";
import { fieldArea, placeholder } from "./styled-css";

const About = ({ programs, handleSelect, type, data }) => {
  const [selected, setSelected] = useState("არჩევა");

  const { errors } = useTableContext();

  return (
    <Wrapper>
      <Title image={edit} text="ინფორმაცია პროგრამის შესახებ:" />

      {type === "1" ? (
        <Item>
          <label>პროგრამა:</label>
          <input
            type="text"
            disabled
            value={data.programName}
            placeholder="მიუთითეთ პირადობის მოწმობის ნომერი"
          />
        </Item>
      ) : (
        <>
          <label label="პროგრამა:" />
          <Select
            selected={selected}
            setSelected={setSelected}
            data={programs}
            name="program_id"
            handleSelect={handleSelect}
            text="პროგრამის არჩევა:"
            image={dropdown}
          />
        </>
      )}
      {errors && <div className="text-danger">{errors.program_id}</div>}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  input {
    ${fieldArea}
    ::placeholder {
      ${placeholder}
    }
  }
  p {
    color: #953849;
    max-width: 600px;
  }
  .error-message {
    position: absolute;
    left: 4px;
    top: 100%;
  }
`;

const Item = styled.div`
  max-width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
`;

export default About;
