import { useState } from 'react'
import { useRouter } from 'next/router'
import apiClientProtected from '../../helpers/apiClient'
import { langs } from './../locale'
import { useLocaleContext } from './../context/LocaleContext'
import { useTableContext } from '../context/TableContext'
import SweetAlert2 from 'react-sweetalert2'

const ChangePass = ({result}) => {
    const { locale } = useLocaleContext()
    const {errors, setErrors} = useTableContext()
    const router = useRouter()

    const [success, setSuccess] = useState(false)
    const [swalProps, setSwalProps] = useState({})

    const [fieldData, setFieldData] = useState({
        current_password: '',
        new_password: '',
        new_password_confirmation : ''
    })

    const [touched, setTouched] = useState({
        current_password: false,
        new_password: false,
        new_password_confirmation: false
    })

    const validate = () => {
        let errors = {}
    
        if(!fieldData.current_password) {
            errors.current_password = 'Current password is required'
        }
    
        if(!fieldData.new_password) {
            errors.new_password = 'New password is required'
        } else if(fieldData.new_password.length < 6) {
            errors.new_password = 'The new password must be at least 6 characters.'
        }
    
        if(!fieldData.new_password_confirmation) {
            errors.new_password_confirmation = 'New password confirmation is required'
        } else if(fieldData.new_password_confirmation.length < 6) {
            errors.new_password_confirmation = 'The new password confirmation must be at least 6 characters.'
        } else if(fieldData.new_password_confirmation !== fieldData.new_password) {
            errors.new_password_confirmation = 'The new password confirmation and new password must match.'
        }
    
        return errors
    }

    const handleChange = e => {
        if(e.target.name === 'current_password') {
            setErrors(null)
        }
        setFieldData({...fieldData, [e.target.name]: e.target.value})
    }

    const handleSubmit = async e => {
        e.preventDefault()

        const touchedCopy = {...touched}
        for(let key in touchedCopy) {
            if(validate()[key]) {
                touchedCopy[key] = true
            }
        }
        setTouched(touchedCopy)

        if(Object.entries(validate()).length) {
            return
        }

        const fd = new FormData()
        for(let key in fieldData) {
            fd.append(key, fieldData[key])
        }
        console.log('asdasd')
        
        try {
            const response = await apiClientProtected().post('/update-password', fd)
            console.log(response)
            setErrors(null)
            setSuccess(true)
            setSwalProps({
                show: true,
                title: "წარმატება",
                text: "თქვენი პაროლი წარმატებით შეიცვალა",
                icon: "success",
                confirmButtonColor: "#009ef7",
                didClose: () => console.log(123),
            })
            router.push('/admin/user')
        } catch(err) {
            console.log(err.response.data)
            setErrors(err.response.data)
        }

    }

    return (
        <div className="card mb-5 mb-xl-10" id="kt_profile_details_view">
            <div className="px-9">
                <div className="card-header cursor-pointer" style={{padding: '0 30px'}}>
                    <div className="card-title m-0">
                        <h3 className="fw-bolder m-0">{locale && langs[locale]['change_password']}</h3>
                    </div>
                </div>
            </div>
            <div className="card-body p-9">
                <form onSubmit={handleSubmit}>                
                    <div className="form-group mb-3">
                        <label htmlFor="current_password">{locale && langs[locale]['current_password']}</label>
                        <input 
                            type="text"
                            id="current_password"
                            name="current_password"
                            className="form-control form-control-solid" 
                            placeholder={locale && langs[locale]['current_password']}
                            value={fieldData.current_password} 
                            onBlur={(e) => setTouched({...touched, [e.target.name]: true})}
                            onChange={handleChange} />
                        <div className="text-danger">
                            {validate() && validate().current_password && touched.current_password ? validate().current_password : '' }
                        </div>
                        <div className="text-danger">
                            {errors && errors.error }
                        </div>
                    </div>
                    
                    <div className="form-group mb-3">
                        <label htmlFor="new_password">{locale && langs[locale]['new_password']}</label>
                        <input 
                            type="password" 
                            className="form-control form-control-solid" 
                            placeholder={locale && langs[locale]['new_password']}
                            id="new_password"
                            name="new_password"
                            value={fieldData.new_password} 
                            onBlur={(e) => setTouched({...touched, [e.target.name]: true})}
                            onChange={handleChange} />
                        <div className="text-danger">
                            {validate() && validate().new_password && touched.new_password ? validate().new_password : '' }
                        </div>
                    </div>
                    
                    <div className="form-group mb-3">
                        <label htmlFor="new_password_confirmation">{locale && langs[locale]['confirm_new_password']}</label>
                        <input 
                            type="password" 
                            className="form-control form-control-solid" 
                            id="new_password_confirmation"
                            name="new_password_confirmation"
                            placeholder={locale && langs[locale]['confirm_new_password']}
                            value={fieldData.new_password_confirmation} 
                            onBlur={(e) => setTouched({...touched, [e.target.name]: true})}
                            onChange={handleChange} />
                        <div className="text-danger">
                            {validate() && validate().new_password_confirmation && touched.new_password_confirmation ? validate().new_password_confirmation : '' }
                        </div>
                    </div>
                    
                    <div className="form-group mb-3">
                        <button className='btn btn-primary' type="submit">
                            { locale && langs[locale]['save'] }
                        </button>
                    </div>
                </form>
                {
                    success && <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
                }
            </div>
        </div>
    );
}
 
export default ChangePass;