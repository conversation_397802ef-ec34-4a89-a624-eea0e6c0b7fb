import Link from "next/link";
import { useEffect, useState, useRef } from "react";
import useAuth from "../custom_hooks/useAuth";
import apiClientProtected from "../../helpers/apiClient";
import { langs } from "./../locale";
import { useLocaleContext } from "./../context/LocaleContext";
import { MdInsertDriveFile } from "react-icons/md";
import { useUserContext } from "./../context/UserContext";

const ProfileBody = ({ result }) => {
  //   const [user, setUser] = useState({});
  const { locale } = useLocaleContext();
  const { user } = useUserContext();

  useEffect(() => {
    console.log(user);
    // const { user: userData } = useAuth(); // ese ?
    // console.log(userData);
    // const fetchData = async () => {
    //   const response = await apiClientProtected().get(
    //     `/administration/personal-info?user_id=${userData.id}`
    //   );
    //   console.log(response.data);
    //   setUser(response.data);
    // };
    // fetchData();
    // console.log(result);
  }, []);

  return (
    <div className="card mb-5 mb-xl-10" id="kt_profile_details_view">
      <div className="px-9">
        <div
          className="card-header cursor-pointer"
          style={{ padding: "0 30px" }}
        >
          <div className="card-title m-0">
            <h3 className="fw-bolder m-0">
              {locale && langs[locale]["profile"]}
            </h3>
          </div>
          <div className="d-flex gap-2">
            <Link href="/admin/user/change-password">
              <a className="btn btn-primary align-self-center">
                {locale && langs[locale]["change_password"]}
              </a>
            </Link>
            <Link href="/admin/user/edit">
              <a className="btn btn-primary align-self-center">
                {locale && langs[locale]["profile_edit"]}
              </a>
            </Link>
          </div>
        </div>
      </div>
      <div className="card-body p-9">
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["first_name"]}
          </label>
          <div className="col-lg-8">
            <span className="fw-bolder fs-6 text-dark">{user.first_name}</span>
          </div>
        </div>
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["last_name"]}
          </label>
          <div className="col-lg-8 fv-row">
            <span className="fw-bolder fs-6">{user.last_name}</span>
          </div>
        </div>
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["id_number"]}
            <i
              className="fas fa-exclamation-circle ms-1 fs-7"
              data-bs-toggle="tooltip"
              title="Phone number must be active"
            ></i>
          </label>
          <div className="col-lg-8 d-flex align-items-center">
            <span className="fw-bolder fs-6 me-2">{user.identity_number}</span>
            <span className="badge badge-success">Verified</span>
          </div>
        </div>
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["phone"]}
          </label>
          <div className="col-lg-8">
            <Link href="/">
              <a className="fw-bolder fs-6 text-dark text-hover-primary">
                {user.phone}
              </a>
            </Link>
          </div>
        </div>
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["email"]}
            <i
              className="fas fa-exclamation-circle ms-1 fs-7"
              data-bs-toggle="tooltip"
              title="Country of origination"
            ></i>
          </label>
          <div className="col-lg-8">
            <span className="fw-bolder fs-6 text-dark">{user.email}</span>
          </div>
        </div>
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["admin_position"]}
          </label>
          <div className="col-lg-8">
            <span className="fw-bolder fs-6 text-dark">
              {user.administration_position &&
                user.administration_position.name_ka}
            </span>
          </div>
        </div>
        <div className="row mb-10">
          <label className="col-lg-4 fw-bold text-muted">
            {locale && langs[locale]["admin_school"]}
          </label>
          <div className="col-lg-8">
            <span className="fw-bold fs-6">{user.school}</span>
          </div>
        </div>
        <div className="row mb-7">
          <label className="col-lg-4 fw-bold text-muted d-flex align-items-center">
            CV
          </label>
          <div className="col-lg-8">
            <a
              href={`${process.env.NEXT_PUBLIC_STORAGE}${user.cv}`}
              style={{ marginLeft: "8px" }}
              target="_blank"
            >
              <MdInsertDriveFile size={32} style={{ color: "#009ef7" }} />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileBody;
