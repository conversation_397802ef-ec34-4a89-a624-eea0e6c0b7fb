import styled from "styled-components";
import { logout, profile, lock } from "../ui/Header/headerSvg";
import ProfileActiveClass from "./ProfileActiveClass";
import { useRouter } from "next/router";
import Cookies from "js-cookie";
import apiClientProtected from "../../helpers/apiClient";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";

const LecturerProfileNavigation = ({ navigationHandler }) => {
  const router = useRouter();
  const { locale } = useLocaleContext();

  const logOut = async (e) => {
    e.preventDefault();
    const token = Cookies.get("token");
    // console.log(token, apiClientProtected); return
    try {
      await apiClientProtected().get("/auth/logout");
      localStorage.removeItem("user");
      Cookies.remove("token");
      router.push("/login");
    } catch (error) {
      console.log(error.response.data);
    }
  };
  return (
    <Navigation>
      <ul>
        <li onClick={navigationHandler}>
          <ProfileActiveClass href="/lecturer/profile">
            {profile} <p>{locale && langs[locale]["profile"]}</p>
          </ProfileActiveClass>
        </li>
        <li>
          <ProfileActiveClass href="/lecturer/edit">
            {lock} <p>{locale && langs[locale]["change_password"]}</p>
          </ProfileActiveClass>
        </li>
        <li onClick={logOut}>
          {logout} <p>{locale && langs[locale]["logout"]}</p>
        </li>
      </ul>
    </Navigation>
  );
};

const Navigation = styled.div`
  max-width: 220px;
  width: 100%;
  min-height: 100vh;
  height: 100%;
  background-color: #ffffff;
  padding: 27px 17px;
  border-radius: 10px;
  margin-right: 10px;
  li {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    cursor: pointer;
    p {
      font-family: "FiraGO", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
      display: flex;
      align-items: center;
      letter-spacing: -0.03em;
    }
    svg {
      margin-right: 12px;
    }
  }
`;

export default LecturerProfileNavigation;
