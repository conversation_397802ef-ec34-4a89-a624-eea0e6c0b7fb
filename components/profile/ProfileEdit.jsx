import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/router";
import useAuth from "../custom_hooks/useAuth";
import { useUserContext } from "../context/UserContext";
import apiClientProtected from "../../helpers/apiClient";
import File from "/public/assets/media/cloud-upload.svg";
import styled from "styled-components";
import Image from "next/image";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { MdPhotoCamera, MdInsertDriveFile } from "react-icons/md";
import { Formik, Field, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import SweetAlert2 from "react-sweetalert2";
import { useTableContext } from "../context/TableContext";

const ProfileEdit = () => {
  const router = useRouter();
  const { errors, setErrors } = useTableContext();

  // lang file
  const { locale } = useLocaleContext();
  const imageRef = useRef(null);
  const cvRef = useRef(null);
  const { user: userData, saveUser } = useUserContext();

  const [success, setSuccess] = useState(false);
  const [image, setImage] = useState("");
  const [preview, setPreview] = useState("");
  const [cv, setCv] = useState("");
  const [cvPreview, setCvPreview] = useState("");
  const [cvName, setCvName] = useState("");
  const [swalProps, setSwalProps] = useState({});
  const [permissions, setPermissions] = useState([]);

  const [user, setUser] = useState({
    first_name: "",
    last_name: "",
    identity_number: "",
    phone: "",
    email: "",
    administration_position: {
      name_ka: "",
    },
    administration_item: "",
    cv: "",
    photo: "",
  });

  // Phone validation regex
  const phoneRegExp = /^\d{9}$/;
  const IMAGE_FORMATS = ["image/jpeg", "image/png", "image/gif"];
  const CV_FORMATS = [
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/msword",
  ];

  console.log(userData, "DDDDDDDDDD");

  useEffect(() => {
    const fetchData = async () => {
      const response = await apiClientProtected().get(
        `/administration/personal-info?user_id=${userData.id}`
      );
      setUser(response.data);
    };
    fetchData();
    setPermissions(userData.permissions);
    // setUser(response.data);
  }, []);

  useEffect(() => {
    if (image) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(image);
    }
  }, [image]);

  useEffect(() => {
    if (cv) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setCvName(cv.name);
        setCvPreview(reader.result);
      };
      reader.readAsDataURL(cv);
    }
  }, [cv]);

  const handleChange = (e) => {
    if (e.target.name === "photo") {
      setImage(e.target.files[0]);
      setUser({ ...user, [e.target.name]: e.target.files[0] });
    } else if (e.target.name === "cv") {
      setCv(e.target.files[0]);

      setUser({ ...user, [e.target.name]: e.target.files[0] });
    } else if (e.target.name === "administration_position") {
      setUser((prevState) => ({
        ...prevState,
        administration_position: {
          ...prevState.administration_position,
          name_ka: e.target.value,
        },
      }));
    } else {
      setUser({ ...user, [e.target.name]: e.target.value });
    }
  };

  const triggerFile = (name) => {
    if (name === "cv") {
      cvRef.current.click();
    } else {
      imageRef.current.click();
    }
  };

  const handleSubmit = async (values) => {
    console.log(values, "value");
    const fd = new FormData();

    fd.append("_method", "PUT");
    if (image) {
      fd.append("photo", values.photo);
    }

    if (values.cv && typeof values.cv !== "string") {
      fd.append("cv", cv);
    }

    fd.append("first_name", values.first_name);
    fd.append("email", values.email);
    fd.append("phone", values.phone);

    try {
      const response = await apiClientProtected().post(
        `/administration/update-personal-info/${userData.id}`,
        fd
      );
      // const permissions = [...user.permissions];
      console.log({ ...response.data }, user.permissions);
      saveUser({ ...response.data, permissions });
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["update_alert"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });
      router.push("/admin/user");
    } catch (error) {
      console.log(error.response);
      setErrors(error.response.data.errors);
    }
  };

  return (
    <div className="card mb-5 mb-xl-10">
      <div className="px-9">
        <div
          className="card-header border-0 cursor-pointer"
          role="button"
          data-bs-toggle="collapse"
          data-bs-target="#kt_account_profile_details"
          aria-expanded="true"
          aria-controls="kt_account_profile_details"
        >
          <div className="card-title m-0">
            <h3 className="fw-bolder m-0">
              {locale && langs[locale]["profile_edit"]}
            </h3>
          </div>
        </div>
      </div>
      <div id="kt_account_profile_details" className="collapse show">
        <Formik
          initialValues={user}
          enableReinitialize={true}
          validationSchema={Yup.object({
            first_name: Yup.string().required("შეიყვანეთ სახელი"),
            email: Yup.string()
              .email("ელ-ფოსტის ფორმატი არასწორია")
              .required("შეიყვანეთ ელ-ფოსტა"),
            photo: Yup.mixed()
              .nullable()
              .notRequired()
              .test(
                "FILE_FORMAT",
                "ატვირთული ფაილის ფორმატი არასწორია, გთხოვთ გამოიყენოთ .jpeg, .png ან .gif ფაილები",
                (value) =>
                  typeof value === "string" ||
                  (value && IMAGE_FORMATS.includes(value.type))
              ),
            cv: Yup.mixed()
              .nullable()
              .notRequired()
              .test(
                "FILE_FORMAT",
                "ატვირთული ფაილის ფორმატი არასწორია, გთხოვთ გამოიყენოთ .pdf ფაილები",
                (value) =>
                  typeof value === "string" ||
                  (value && CV_FORMATS.includes(value.type))
              ),
            phone: Yup.string()
              .matches(phoneRegExp, "ტელეფონის ველი არასწორია")
              .required("შეიყვანეთ ელ-ფოსტა"),
          })}
          onSubmit={(values, { setSubmitting }) => {
            handleSubmit(values);
          }}
        >
          {({ isSubmitting, values }) => (
            <Form className="form">
              <div className="card-body border-top p-9">
                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    {locale && langs[locale]["photo"]}
                  </label>
                  <div className="col-lg-8">
                    <div
                      className="image-input image-input-outline profile-image__edit"
                      onClick={() => triggerFile("image")}
                      data-kt-image-input="true"
                      style={{
                        backgroundImage: "url(/assets/media/avatars/blank.png)",
                      }}
                    >
                      {!user.photo && !preview && (
                        <img
                          src={"/assets/media/avatars/blank.png"}
                          className="image-input-wrapper w-125px h-125px cursor-pointer"
                          alt=""
                        />
                      )}
                      {preview && (
                        <PrevImage
                          src={preview}
                          width="125"
                          height="125"
                          alt="user-image"
                        />
                      )}

                      {user.photo && !preview && (
                        <img
                          src={`${process.env.NEXT_PUBLIC_STORAGE}/${user.photo}`}
                          className="w-125px h-125px object-fit-cover"
                          alt="profile-photo"
                        />
                      )}
                      <div className="image-layer" style={{ color: "#ccc" }}>
                        <MdPhotoCamera size={32} />
                      </div>
                    </div>
                    <ErrorMessage
                      name="photo"
                      component="div"
                      className="text-danger"
                    />
                  </div>
                  <input
                    type="file"
                    style={{ display: "none" }}
                    accept="image/png, image/gif, image/jpeg"
                    onChange={handleChange}
                    ref={imageRef}
                    name="photo"
                  />
                </div>
                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label required fw-bold fs-6">
                    {locale && langs[locale]["full_name"]}
                  </label>
                  <div className="col-lg-8">
                    <div className="row">
                      <div className="col-lg-6 fv-row">
                        <Field
                          className="form-control form-control-lg form-control-solid mb-3 mb-lg-0"
                          type="text"
                          name="first_name"
                        />
                        <ErrorMessage
                          name="first_name"
                          component="div"
                          className="text-danger"
                        />
                      </div>
                      <div className="col-lg-6 fv-row">
                        <Field
                          className="form-control form-control-lg form-control-solid"
                          type="text"
                          name="last_name"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    <span>{locale && langs[locale]["id_number"]}</span>
                  </label>
                  <div className="col-lg-8 fv-row">
                    <Field
                      className="form-control form-control-lg form-control-solid"
                      type="text"
                      name="identity_number"
                      disabled
                    />
                  </div>
                </div>
                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    <span className="required">
                      {locale && langs[locale]["phone"]}
                    </span>
                  </label>
                  <div className="col-lg-8 fv-row">
                    <Field
                      className="form-control form-control-lg form-control-solid"
                      type="text"
                      name="phone"
                    />
                    <ErrorMessage
                      name="phone"
                      component="div"
                      className="text-danger"
                    />
                  </div>
                </div>
                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    <span className="required">
                      {locale && langs[locale]["email"]}
                    </span>
                  </label>
                  <div className="col-lg-8 fv-row">
                    <Field
                      className="form-control form-control-lg form-control-solid"
                      type="text"
                      name="email"
                    />
                    <ErrorMessage
                      name="email"
                      component="div"
                      className="text-danger"
                    />
                  </div>
                </div>

                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    <span>{locale && langs[locale]["admin_position"]}</span>
                  </label>
                  <div className="col-lg-8 fv-row">
                    {/* <Field
                      className="form-control form-control-lg form-control-solid"
                      type="text"
                      value={values.administration_position.name_ka}
                      name="administration_position"
                      disabled
                    /> */}
                  </div>
                </div>

                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    <span>{locale && langs[locale]["admin_school"]}</span>
                  </label>
                  <div className="col-lg-8 fv-row">
                    <Field
                      className="form-control form-control-lg form-control-solid"
                      type="text"
                      name="administration_item"
                      value={values.administration_item || ""}
                      disabled
                    />
                  </div>
                </div>
                <div className="row mb-6">
                  <label className="col-lg-4 col-form-label fw-bold fs-6">
                    <span className="d-inline-block">CV</span>
                    <a
                      href={`${process.env.NEXT_PUBLIC_STORAGE}${user.cv}`}
                      style={{ marginLeft: "8px" }}
                      target="_blank"
                    >
                      <MdInsertDriveFile
                        size={32}
                        style={{ color: "#009ef7" }}
                      />
                    </a>
                  </label>
                  <div
                    className="col-lg-8 fv-row d-flex align-items-center cursor-pointer position-relative"
                    onClick={() => triggerFile("cv")}
                  >
                    <input
                      type="file"
                      ref={cvRef}
                      style={{ display: "none" }}
                      onChange={handleChange}
                      accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
                      name="cv"
                    />
                    <CvLogo>
                      <Image src={File} alt="" />
                    </CvLogo>
                    <span style={{ marginRight: "2rem" }}>
                      {cv ? cvName : "ატვირთვა"}
                    </span>
                    <ErrorMessage
                      name="cv"
                      component="div"
                      className="text-danger"
                    />
                  </div>
                  {errors && <div className="text-danger">{errors.cv}</div>}
                </div>
              </div>
              <div className="card-footer d-flex justify-content-end py-6 px-9">
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting}
                >
                  {locale && langs[locale]["edit"]}
                </button>
              </div>
            </Form>
          )}
        </Formik>

        {success && (
          <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
        )}
      </div>
    </div>
  );
};

export default ProfileEdit;

const CvLogo = styled.div`
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  img {
    width: 100%;
  }
`;

const PrevImage = styled.img`
  object-fit: cover;
  width: 125px;
  height: 125px;
`;
