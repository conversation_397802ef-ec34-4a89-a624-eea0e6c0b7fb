import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { GrClose } from "react-icons/gr";
import styled from "styled-components";
import Link from "next/link";
import Image from "next/image";
import { useTableContext } from "../../context/TableContext";
import logo from "/public/assets/media/logo.svg";
import ActiveLink from "./ActiveLink";
import { useLocaleContext } from "../../context/LocaleContext";
import apiClientProtected from "../../../helpers/apiClient";
import { FaTiktok } from "react-icons/fa";
import BookModal from "../../student/library/BookModal";

import {
  mobile,
  instagram,
  facebook,
  linkedin,
  twitter,
  youtube,
  arrowdown,
  email,
} from "./sidebarSvg";
import LangSwitcher from "../Header/LangSwitcher";

const SidebarComponent = ({
  sidebarHandler,
  openSidebar,
  links,
  routeName,
}) => {
  // const [showDropdown, setShowDropdown] = useState(false);
  const { pathname } = useRouter();
  const [linkList, setLinkList] = useState([]);
  const { locale } = useLocaleContext();
  const router = useRouter();
  const { pageInfo } = useTableContext();
  const [messageCount, setMessageCount] = useState(null);
  const [showMinorModal, setShowMinorModal] = useState(false);
  const [studentData, setStudentData] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState(null);

  // const { links } = useLangHook()

  useEffect(() => {
    setLinkList(links);
  }, [locale]);

  useEffect(() => {
    (async () => {
      try {
        const response = await apiClientProtected().get("messages/unreadCount");
        setMessageCount(response.data.count);
      } catch (err) {
        console.log(err);
      }
    })();
    sidebarHandler(false);
  }, [pathname]);

  // Get student data from localStorage for minor selection
  useEffect(() => {
    if (routeName === "student") {
      try {
        const userData = localStorage.getItem("user");
        if (userData) {
          const user = JSON.parse(userData);
          setStudentData(user);
        }
      } catch (err) {
        console.log("Error getting user data from localStorage:", err);
      }
    }
  }, [routeName]);

  const handleMinorSelection = async (minorId) => {
    setIsSubmitting(true);
    try {
      const response = await apiClientProtected().post("/student/vote-minor", {
        minor_id: minorId,
      });

      if (response.data.success) {
        // Update localStorage with selected minor
        const userData = localStorage.getItem("user");
        if (userData) {
          const user = JSON.parse(userData);
          user.minor_id = minorId;
          localStorage.setItem("user", JSON.stringify(user));
          setStudentData(user);
        }

        setNotification({
          type: "success",
          message: "მაინორი წარმატებით აირჩა!"
        });
        setShowMinorModal(false);
      } else if (response.data.error) {
        setNotification({
          type: "error",
          message: response.data.error
        });
      }
    } catch (err) {
      console.log("Error voting for minor:", err);
      setNotification({
        type: "error",
        message: "შეცდომა მაინორის არჩევისას"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto hide notification after 5 seconds
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);

  const toggleAccordion = (id) => {
    const newLinks = linkList.map((item) => {
      if (id !== item.id) {
        item.isMenuOpen = false;
      } else {
        item.isMenuOpen = !item.isMenuOpen;
      }
      return item;
    });

    setLinkList(newLinks);
  };

  return (
    <NavigationContainer openSidebar={openSidebar}>
      <Logo>
        <Link href={`/${routeName}`}>
          <a>
            <Image src={logo} alt="logo" />
          </a>
        </Link>
      </Logo>

      <SidebarHeader>
        <div className="lang-container">
          <LangSwitcher />
        </div>
        <CloseBtn onClick={() => sidebarHandler(!openSidebar)}>
          <GrClose />
        </CloseBtn>
      </SidebarHeader>
      <nav>
        <ul>
          {linkList.map((item) => (
            <li key={item.id}>
              {item.divider && <Divider></Divider>}
              {item.subMenu ? (
                <>
                  <div
                    onClick={() => toggleAccordion(item.id)}
                    className={`${item.isMenuOpen && "active"}`}
                  >
                    <DropButton isMenuOpen={item.isMenuOpen}>
                      <span>
                        {item.icon}
                        <p>{item.name}</p>
                      </span>
                      <button>{arrowdown}</button>
                    </DropButton>
                  </div>

                  <MiniNavigation
                    isMenuOpen={item.isMenuOpen}
                    className={`${item.isMenuOpen && "side-submenu-open"}`}
                  >
                    {item.subMenu.map((menuItem, index) => (
                      <li key={index}>
                        <Link href={menuItem.href}>
                          <a
                            className={`${
                              router.pathname === menuItem.href ||
                              router.query.semester_id ===
                                menuItem.href.slice(menuItem.href.length - 1)
                                ? "active"
                                : ""
                            }`}
                            target={menuItem.target ? "_blank" : ""}
                          >
                            {menuItem.name}
                          </a>
                        </Link>
                      </li>
                    ))}
                  </MiniNavigation>
                </>
              ) : item.isMinorSelection ? (
                // Check if student has learn_year_id = 8 before showing minor selection
                studentData && studentData.learn_year_id === item.showForLearnYear ? (
                  <div
                    className="minor-selection-item"
                    onClick={() => setShowMinorModal(true)}
                  >
                    <span>
                      {item.icon}
                      <p>{item.name}</p>
                    </span>
                  </div>
                ) : null
              ) : (
                <div
                  className={`${
                    (item.href === "/lecturer/chat" ||
                      item.href === "/student/chat") &&
                    messageCount
                      ? "message-count-lecturer-link"
                      : ""
                  } ${router.pathname === item.href && "active"}`}
                  onClick={() => toggleAccordion(item.id)}
                >
                  <Link href={item.href}>
                    <a
                      className={`${router.pathname === item.href && "active"}`}
                      target={item.target ? "_blank" : ""}
                    >
                      {item.icon}
                      <p>{item.name}</p>
                    </a>
                  </Link>
                  {(item.href === "/lecturer/chat" ||
                    item.href === "/student/chat") &&
                  messageCount ? (
                    <span className="message-count-item">{messageCount}</span>
                  ) : null}
                </div>
              )}
            </li>
          ))}
        </ul>
        <NavSocial>
          <SocialLinks>
            <li>
              <a
                href="mailto:<EMAIL>"
                target="_blank"
                className="social-link"
              >
                {email}
              </a>
            </li>
            <li>
              <a
                href="https://www.facebook.com/gipa.ge"
                target="_blank"
                className="social-link"
              >
                {facebook}
              </a>
            </li>
            <li>
              <a
                href="https://www.instagram.com/gipa.ge"
                target="_blank"
                className="social-link"
              >
                {instagram}
              </a>
            </li>
            <li>
              <a
                href="https://www.linkedin.com/school/11471664"
                target="_blank"
                className="social-link"
              >
                {linkedin}
              </a>
            </li>
            <li>
              <a
                href="https://www.tiktok.com/@gipa.ge"
                target="_blank"
                className="social-link"
              >
                <FaTiktok />
              </a>
            </li>
            <li>
              <a
                href="https://www.twitter.com/gipaofficial?lang=en"
                target="_blank"
                className="social-link"
              >
                {twitter}
              </a>
            </li>
            <li>
              <a
                href="https://www.youtube.com/@GIPACSJMM/featured"
                target="_blank"
                className="social-link"
              >
                {youtube}
              </a>
            </li>
          </SocialLinks>
          <span></span>
          <p>© {new Date().getFullYear()} - GIPA - All rights reserved</p>
        </NavSocial>
      </nav>

      {/* Minor Selection Modal */}
      {showMinorModal && (
        <BookModal
          open={showMinorModal}
          setOpen={setShowMinorModal}
          title="გთხოვთ, აირჩიეთ მაინორი:"
        >
          <MinorModalContent>
            <MinorOption
              onClick={() => handleMinorSelection(1)}
              disabled={isSubmitting}
              isSelected={studentData?.minor_id === 1}
            >
              ფსიქოლოგია
              {studentData?.minor_id === 1 && <SelectedIndicator>✓</SelectedIndicator>}
            </MinorOption>
            <MinorOption
              onClick={() => handleMinorSelection(2)}
              disabled={isSubmitting}
              isSelected={studentData?.minor_id === 2}
            >
              საერთაშორისო ურთიერთობები
              {studentData?.minor_id === 2 && <SelectedIndicator>✓</SelectedIndicator>}
            </MinorOption>
            <MinorOption
              onClick={() => handleMinorSelection(3)}
              disabled={isSubmitting}
              isSelected={studentData?.minor_id === 3}
            >
              საჯარო მმართველობა
              {studentData?.minor_id === 3 && <SelectedIndicator>✓</SelectedIndicator>}
            </MinorOption>
            {isSubmitting && <LoadingText>იტვირთება...</LoadingText>}
          </MinorModalContent>
        </BookModal>
      )}

      {/* Notification */}
      {notification && (
        <NotificationContainer type={notification.type}>
          <NotificationText>{notification.message}</NotificationText>
          <CloseNotification onClick={() => setNotification(null)}>
            ×
          </CloseNotification>
        </NotificationContainer>
      )}
    </NavigationContainer>
  );
};

const NavigationContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  z-index: 20;
  width: 256px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 25px;
  border-right: 1px solid #dae2f1;
  background: #f8f8f8;
  overflow: scroll;
  ::-webkit-scrollbar {
    width: 4px;
  }
  // &::-webkit-scrollbar-track {
  //   background: red;
  // }

  ::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  :hover::-webkit-scrollbar-thumb {
    background-color: #c8d2e9;
    border-radius: 8px;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  @media (max-width: 980px) {
    position: fixed;
    left: inherit;
    right: 0;
    height: 100%;
    max-width: 45%;
    width: 100%;
    overflow-y: scroll;
    transform: ${(props) =>
      props.openSidebar === true ? "translateX(0%)" : `translateX(100%)`};
    transition: all 300ms;
  }
  @media (max-width: 640px) {
    max-width: 265px;
  }
  nav {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 3rem;
    @media (max-width: 980px) {
      margin-top: 0;
    }
    ul {
      li {
        width: 100%;
        display: flex;
        flex-direction: column;

        span {
          display: flex;
          padding: 1rem;
          gap: 1rem;
          /* color: #57618D; */

          cursor: pointer;
        }
        p {
          color: #57618d;
        }
        div {
          &:hover {
            background-color: #7ea4ff;
          }
        }
        a {
          display: flex;
          align-items: center;
          padding: 1rem;
          gap: 1rem;
          width: 100%;
          color: #57618d;
          transition: none;
          &:hover {
            background-color: #7ea4ff;
            transition: none;
          }
          &:hover p {
            color: #fff;
          }
        }
        .active {
          background: #7ea4ff;
          color: #fff;
          svg {
            path {
              fill: #ffffff;
              stroke: transparent;
            }
            ellipse {
              fill: #57618d;
            }
          }
          a,
          p {
            color: #fff;
          }
        }
        svg {
          max-width: 22px;
          width: 100%;
          path {
            fill: #57618d;
          }
          ellipse {
            fill: #ffffff;
          }
        }
        svg path {
          fill: ${({ isMenuOpen }) => (isMenuOpen ? "#fff" : "#57618D")};
        }

        .minor-selection-item {
          cursor: pointer;
          &:hover {
            background-color: #7ea4ff;
          }
          &:hover p {
            color: #fff;
          }
          span {
            display: flex;
            align-items: center;
            padding: 1rem;
            gap: 1rem;
            color: #57618d;
          }
        }
      }
    }
  }
`;

const SidebarHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  @media (max-width: 992px) {
    padding: 1.5rem;
  }

  .lang-container {
    display: none;
    @media (max-width: 992px) {
      display: block;
    }
  }
`;

const Divider = styled.div`
  height: 1.4px;
  margin: 1rem;
  background: #acb0c5;
  width: 40px;
`;
const DropButton = styled.div`
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  button {
    margin-right: 14px;
    color: #fff;
    transform: ${({ isMenuOpen }) =>
      isMenuOpen ? "rotate(180deg)" : "roteta(0deg)"};
    transition: all 300ms;
  }
  svg {
    fill: #fff;
  }
  &:hover {
    background: #7ea4ff;
    color: #fff;
  }
  &:hover p {
    color: #fff;
  }
`;
const Logo = styled.div`
  width: 100%;
  min-height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18px;
  @media (max-width: 980px) {
    display: none;
  }
`;
const CloseBtn = styled.button`
  display: none;
  svg {
    font-size: 30px;
  }
  @media (max-width: 980px) {
    display: block;
  }
`;
const Line = styled.span`
  display: block;
  height: 1px;
  width: 40px;
  background-color: #acb0c5;
  margin: 8px 0 15px 8px;
`;
const DropDown = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  span {
    display: flex;
    align-items: center;
  }
  button {
    transition: all 300ms;
    transform: ${({ isMenuOpen }) =>
      isMenuOpen ? "rotate(180deg)" : `rotate(0deg)`};
  }
`;
const MiniNavigation = styled.ul`
  max-height: 0px;
  width: 100%;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  background-color: #eff3fa;
  li {
    padding: 0 1rem;
    a {
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 14px;
      color: #181c32 !important;
      padding: 5px 0;
      display: flex;
      align-items: center;
      margin-right: 10px;
      &:hover {
        background: transparent !important;
        color: #7ea4ff !important;
      }

      ::before {
        content: "";
        height: 6px;
        width: 6px;
        border-radius: 50%;
        display: block;
        background-color: #181c32;
      }
    }
  }
`;
const NavSocial = styled.div`
  max-width: 192px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  p {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: center;
    color: #57618d;
  }
`;
const SocialLinks = styled.ul`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 18px;
  border-bottom: solid 1px #acb0c5;
  margin-bottom: 13px;
  cursor: pointer;
  li {
    max-width: 15px;
    width: 100%;
    .social-link {
      padding: 0;
      &:hover {
        background: transparent;
      }
    }
    svg {
      width: 100%;
    }
  }
`;

const MinorModalContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px 0;
`;

const MinorOption = styled.button`
  padding: 15px 20px;
  background-color: ${({ isSelected }) => isSelected ? "#e8f5e8" : "#f8f9fa"};
  border: 2px solid ${({ isSelected }) => isSelected ? "#28a745" : "#e9ecef"};
  border-radius: 8px;
  font-family: "FiraGO", sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:hover {
    background-color: ${({ isSelected }) => isSelected ? "#d4edda" : "#e9ecef"};
    border-color: ${({ isSelected }) => isSelected ? "#28a745" : "#007bff"};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SelectedIndicator = styled.span`
  color: #28a745;
  font-weight: bold;
  font-size: 18px;
`;

const LoadingText = styled.p`
  text-align: center;
  font-family: "FiraGO", sans-serif;
  font-size: 14px;
  color: #666;
  margin: 10px 0;
`;

const NotificationContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 300px;
  max-width: 500px;
  background-color: ${({ type }) =>
    type === "success" ? "#7ea4ff" :
    type === "error" ? "#f8d7da" : "#fff3cd"};
  border: 1px solid ${({ type }) =>
    type === "success" ? "#c3e6cb" :
    type === "error" ? "#f5c6cb" : "#ffeaa7"};
  color: ${({ type }) =>
    type === "success" ? "white" :
    type === "error" ? "#721c24" : "#856404"};
  animation: slideIn 0.3s ease-out;

  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;

const NotificationText = styled.span`
  font-family: "FiraGO", sans-serif;
  font-size: 14px;
  font-weight: 500;
  flex: 1;
`;

const CloseNotification = styled.button`
  background: none;
  border: none;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 10px;
  color: inherit;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
`;

export default SidebarComponent;
