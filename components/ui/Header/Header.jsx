import { useRef, useState, useEffect } from "react";
// import { AnimatePresence, motion } from 'framer-motion'
import Styles from "./header.module.css";
import { useRouter } from "next/router";
import { useUserContext } from "./../../context/UserContext";
import { useLocaleContext } from "./../../context/LocaleContext";
import useAuth from "./../../custom_hooks/useAuth";
import Cookies from "js-cookie";
import Link from "next/link";
import useOutsideClick from "../../custom_hooks/useOutsideClick";
import apiClientProtected from "./../../../helpers/apiClient";
import { langs } from "../../locale";
import { LANG_LIST } from "./headerData";
import { MdMailOutline } from "react-icons/md";

function Header({ openSidebar, setOpenSidebar }) {
  const router = useRouter();
  const { user: userData } = useAuth();
  const [showUserAccount, setShowUserAccount] = useState(false);
  // const [lang, setLang] = useState({id: 1, title: 'ინგლისური', lng: 'en', image: '/assets//media/flags/united-states.svg'})
  const userMenuRef = useRef();
  const { user, getUser } = useUserContext();
  const { locale, lang, handleLocale } = useLocaleContext();

  useEffect(() => {
    getUser();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        await apiClientProtected().get("/check-token-validity");
      } catch (err) {
        localStorage.removeItem("user");
        Cookies.remove("token");
        router.push("/login");
      }
    })();
  }, []);

  const logout = async (e) => {
    e.preventDefault();

    try {
      await apiClientProtected().get("/auth/logout");
      localStorage.removeItem("user");
      Cookies.remove("token");
      router.push("/login");
    } catch (error) {
      console.log(error.response.data);
    }
  };

  useOutsideClick(userMenuRef, () => setShowUserAccount(false));

  return (
    <div id="kt_header" className="header align-items-stretch">
      {/*begin::Container*/}
      <div className="container-fluid d-flex align-items-stretch justify-content-between">
        {/*begin::Aside mobile toggle*/}
        <div
          className="d-flex align-items-center d-lg-none ms-n2 me-2"
          title="Show aside menu"
          onClick={() => setOpenSidebar(!openSidebar)}
        >
          <div
            className="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px"
            id="kt_aside_mobile_toggle"
          >
            {/*begin::Svg Icon | path: icons/duotune/abstract/abs015.svg*/}
            <span className="svg-icon svg-icon-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z"
                  fill="black"
                />
                <path
                  opacity="0.3"
                  d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z"
                  fill="black"
                />
              </svg>
            </span>
            {/*end::Svg Icon*/}
          </div>
        </div>
        {/*end::Aside mobile toggle*/}
        {/*begin::Mobile logo*/}
        <div className="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
          <a href="../../demo1/dist/index.html" className="d-lg-none">
            <img
              alt="Logo"
              src="/assets/media/logos/logo.png"
              className="h-40px logo"
            />
          </a>
        </div>
        {/*end::Mobile logo*/}
        {/*begin::Wrapper*/}
        <div className="d-flex align-items-stretch justify-content-between flex-lg-grow-1">
          {/*begin::Navbar*/}
          <div className="d-flex align-items-stretch" id="kt_header_nav">
            {/*begin::Menu wrapper*/}
            <div className="header-menu align-items-stretch">
              {/*begin::Menu*/}

              {/*end::Menu*/}
            </div>
            {/*end::Menu wrapper*/}
          </div>
          {/*end::Navbar*/}
          {/*begin::Toolbar wrapper*/}
          <div className="d-flex align-items-stretch flex-shrink-0">
            {/*begin::Activities*/}

            {/*end::Activities*/}

            {/*begin::Notifications*/}

            {/*end::Notifications*/}

            {/*begin::Quick links*/}

            {/*end::Quick links*/}
            {/*begin::Theme mode*/}

            {/*end::Theme mode*/}
            {/*begin::User menu*/}
            <div
              className="d-flex align-items-center ms-1 ms-lg-3 position-relative"
              id="kt_header_user_menu_toggle"
            >
              {/*begin::Menu wrapper*/}
              <div
                className="cursor-pointer symbol symbol-30px symbol-md-40px"
                onClick={() => setShowUserAccount(!showUserAccount)}
                ref={userMenuRef}
              >
                {user && user.photo ? (
                  <img
                    className="object-fit-cover"
                    src={`${process.env.NEXT_PUBLIC_STORAGE}/${user.photo}`}
                    alt="user-image"
                  />
                ) : (
                  <img
                    className="object-fit-cover"
                    src="/assets/media/avatars/blank.png"
                    alt="user-image"
                  />
                )}
              </div>
              {/*begin::User account menu*/}
              {
                <div
                  className={`${Styles.userDropDown} ${
                    showUserAccount && Styles.hide
                  }`}
                >
                  <div className={Styles.dropdownHead}>
                    <div className={Styles.dropdownImg}>
                      {user && user.photo ? (
                        <img
                          className="object-fit-cover"
                          src={`${process.env.NEXT_PUBLIC_STORAGE}/${user.photo}`}
                          alt="user-image"
                        />
                      ) : (
                        <img
                          className="object-fit-cover"
                          src="/assets/media/avatars/blank.png"
                          alt="user-image"
                        />
                      )}
                    </div>
                    <div className={Styles.userInfo}>
                      {user && (
                        <h4 className={Styles.userName}>
                          {user.first_name} {user.last_name}
                          {/* <span className={Styles.userBadge}>Pro</span> */}
                        </h4>
                      )}
                      {user && (
                        <Link href="/user">
                          <a className={Styles.emailLink}>{user.email}</a>
                        </Link>
                      )}
                    </div>
                  </div>
                  <div className={Styles.separator}></div>
                  <ul className={Styles.dropdownMenu}>
                    <li className={Styles.dropdownItem}>
                      <Link href="/admin/user">
                        <a className={Styles.dropdownLink}>
                          {locale && langs[locale]["profile"]}
                        </a>
                      </Link>
                    </li>
                    <li
                      className={`${Styles.dropdownItem} ${Styles.langdropItem}`}
                    >
                      <div
                        className={`${Styles.dropdownLink} ${Styles.langdropLink}`}
                      >
                        <span>{locale && langs[locale]["language"]}</span>
                        <span className="fs-8 rounded bg-light px-3">
                          {lang[`name_${locale}`]}
                          <img
                            className="w-15px h-15px rounded-1 ms-2"
                            src={`${lang.image}`}
                            alt="metronic"
                          />
                        </span>
                      </div>

                      <ul className={Styles.dropdownList}>
                        {LANG_LIST.map((langItem) => (
                          <li
                            className={Styles.dropdownListItem}
                            onClick={() => handleLocale(langItem)}
                            key={langItem.id}
                          >
                            <div className={Styles.drListLink}>
                              <span className={Styles.drListImg}>
                                <img src={`${langItem.image}`} alt="ge" />
                              </span>
                              <span>
                                {locale && langItem[`name_${locale}`]}
                              </span>
                            </div>
                          </li>
                        ))}
                        {/* <li className={Styles.dropdownListItem}>
                                <Link href="/">
                                    <a className={Styles.drListLink}>
                                        <span className={Styles.drListImg}>
                                            <img src="/assets//media/flags/united-states.svg" alt="en" />
                                        </span>
                                        <span>ინგლისური</span>
                                    </a>
                                </Link>
                            </li> */}
                      </ul>
                    </li>
                    <div className={Styles.separator}></div>
                    <li className={Styles.dropdownItem}>
                      <Link href="/" className={Styles.dropdownLink}>
                        <a className={Styles.dropdownLink} onClick={logout}>
                          {locale && langs[locale]["logout"]}
                        </a>
                      </Link>
                    </li>
                  </ul>
                </div>
              }
              {/*end::User account menu*/}
              {/*end::Menu wrapper*/}
            </div>
            {/*end::User menu*/}
            {/*begin::Header menu toggle*/}
            <div
              className="d-flex align-items-center d-lg-none ms-2 me-n3"
              title="Show header menu"
            >
              <div
                className="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px"
                id="kt_header_menu_mobile_toggle"
              >
                {/*begin::Svg Icon | path: icons/duotune/text/txt001.svg*/}
                <span className="svg-icon svg-icon-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      d="M13 11H3C2.4 11 2 10.6 2 10V9C2 8.4 2.4 8 3 8H13C13.6 8 14 8.4 14 9V10C14 10.6 13.6 11 13 11ZM22 5V4C22 3.4 21.6 3 21 3H3C2.4 3 2 3.4 2 4V5C2 5.6 2.4 6 3 6H21C21.6 6 22 5.6 22 5Z"
                      fill="black"
                    />
                    <path
                      opacity="0.3"
                      d="M21 16H3C2.4 16 2 15.6 2 15V14C2 13.4 2.4 13 3 13H21C21.6 13 22 13.4 22 14V15C22 15.6 21.6 16 21 16ZM14 20V19C14 18.4 13.6 18 13 18H3C2.4 18 2 18.4 2 19V20C2 20.6 2.4 21 3 21H13C13.6 21 14 20.6 14 20Z"
                      fill="black"
                    />
                  </svg>
                </span>
                {/*end::Svg Icon*/}
              </div>
            </div>
            {/*end::Header menu toggle*/}
          </div>
          {/*end::Toolbar wrapper*/}
        </div>
        {/*end::Wrapper*/}
      </div>
      {/*end::Container*/}
    </div>
  );
}

export default Header;
