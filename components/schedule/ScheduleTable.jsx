import styled from "styled-components";
import DaySchedule from "../lecturer/schedule/DaySchedule";
import DayScheduleMob from "../lecturer/schedule/DayScheduleMob";
import { schedule } from "../ui/Sidebar/sidebarSvg";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import NoData from "../ui/NoData";
import apiClientProtected from "../../helpers/apiClient";
import StudentLoader from "../ui/StudentLoader";
import { getBirthDate, dateFormat } from "../../helpers/funcs";

const ScheduleTable = ({ url, type }) => {
  const [lectures, setLectures] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { locale } = useLocaleContext();

  useEffect(() => {
    const getData = async () => {
      try {
        const response = await apiClientProtected().get(url);
        setLectures(Object.entries(response.data.data));
        setIsLoading(false);
      } catch (err) {
        console.log(err);
        setIsLoading(false);
      }
    };

    const date = new Date();
    console.log(date.getDate(), date.getMonth());

    getData();
  }, []);

  return (
    <>
      <Container>
        {isLoading ? (
          <StudentLoader />
        ) : lectures.length ? (
          <>
            {/* <Link href="/lecturer/schedule/4">Schedule Link</Link> */}
            <Intro>
              <h4>{locale && langs[locale]["table"]}</h4>
              <button>
                {schedule}
                {getBirthDate(dateFormat(null, null, "-"))}
              </button>
            </Intro>
            <WebContainer>
              {lectures.map((item) => (
                <DaySchedule
                  key={item[0]}
                  type={type}
                  day={item[0]}
                  data={item[1]}
                />
              ))}
            </WebContainer>
            <MobContainer>
              {lectures.map((item) => (
                <DayScheduleMob
                  key={item[0]}
                  type={type}
                  day={item[0]}
                  data={item[1]}
                />
              ))}
              {/* <DayScheduleMob  /> */}
              {/* <DayScheduleMob /> */}
            </MobContainer>
          </>
        ) : (
          <NoData />
        )}
      </Container>
    </>
  );
};

export default ScheduleTable;

const Container = styled.div`
  width: 100%;
  height: 100vh;
  padding: 25px;
  @media (max-width: 576px) {
    padding: 20px 15px;
  }
  @media (max-width: 375px) {
    padding: 20px 10px;
  }
`;

const WebContainer = styled.div`
  background-color: #f8f8f8;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.06), 0px 4px 4px rgba(0, 0, 0, 0.04),
    0px 0px 1px rgba(0, 0, 0, 0.04);
  border-radius: 14px;
`;

const MobContainer = styled.div``;

const Intro = styled.div`
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  h4 {
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    letter-spacing: -0.03em;
    color: #953849;
  }
  button {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    font-family: "FiraGO", sans-serif;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 24px;
    letter-spacing: -0.25px;
    color: #ffffff;
    background-color: #e7526d;
    border-radius: 6px;
    transition: all 0.5s ease;
    display: none;
    @media (max-width: 768px) {
      display: block;
    }
    :hover {
      cursor: pointer;
      background-color: #e08999;
    }
    svg {
      margin-right: 8px;
      path {
        fill: #ffffff;
        stroke: #ffffff;
      }
      ellipse {
        fill: #e7526d;
      }
    }
  }
`;
