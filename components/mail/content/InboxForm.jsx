import dynamic from "next/dynamic";
import { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import { langs } from "../../locale";
import { MdClose } from "react-icons/md";
import { useLocaleContext } from "../../context/LocaleContext";
import { DROPDOWN_DATA } from "../MessagingSidebarLinks";
import SweetAlert2 from "react-sweetalert2";
import useOutsideClick from "../../custom_hooks/useOutsideClick";
import apiClientProtected from "../../../helpers/apiClient";
import { TYPES } from "./../MessagingSidebarLinks";
import { MultiSelect } from "react-multi-select-component";
import ButtonLoader from "../../ui/ButtonLoader";
import BaseFilterSelect from "./../../base/BaseFilterSelect";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

const InboxForm = ({ formType, messageId, getMessages }) => {
  const { locale } = useLocaleContext();
  const [type, setType] = useState("school");
  const [fieldData, setFieldData] = useState({
    title: "",
    body: "",
    attachments: [],
  });

  const [searchString, setSearchString] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [success, setSuccess] = useState(false);
  const [schools, setSchools] = useState([]);
  const [selectedSchools, setSelectedSchools] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [selectedPrograms, setSelectedPrograms] = useState([]);
  const [groups, setGroups] = useState([]);
  const [flows, setFlows] = useState([]);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [selectedFlows, setSelectedFlows] = useState([]);
  const [users, setUsers] = useState([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  // const [files, setFiles] = useState(['1'])
  const [selectedUsers, setSelectedUsers] = useState([]);
  const FileUpload = useRef(null);
  const elementRef = useRef(null);
  const hiddenButton = useRef(null);

  useEffect(() => {
    const getType = async () => {
      if (type === "school") {
        setSelectedUsers([]);
        const schoolResponse = await apiClientProtected().get(`/schools`);
        const programResponse = await apiClientProtected().get(`/programs`);
        const sc = schoolResponse.data.schools.data.map((item) => {
          item.label = item.name_ka;
          item.value = item.id;
          return item;
        });
        // fd.append('messages', [1, 2, 3])
        // fd.append('message_status_id', 1)
        // axios.post('/change-status', fd)
        // api/messages/delete?messages=[1,2,3] GET
        const pr = programResponse.data.programs.data.map((item) => {
          item.label = item.name_ka;
          item.value = item.id;
          return item;
        });
        setSchools(sc);
        setPrograms(pr);
        console.log(schoolResponse, programResponse);
      } else if (type === "group") {
        setSelectedSchools([]);
        setSelectedPrograms([]);
        setSelectedUsers([]);
        const schoolResponse = await apiClientProtected().get(`/schools`);
        const sc = schoolResponse.data.schools.data.map((item) => {
          item.label = item.name_ka;
          item.value = item.id;
          return item;
        });
        setSchools(sc);
      }
    };
    getType();
  }, [type]);

  useEffect(() => {
    const handleGroups = async () => {
      if (type === "group" || (type === "school" && selectedSchools.length)) {
        const response = await apiClientProtected().get(
          `/messages/filter/schools?schools=[${selectedSchools.map(
            (item) => item
          )}]`
        );
        const pr = response.data.map((item) => {
          item.label = item.name_ka;
          item.value = item.id;
          return item;
        });
        setPrograms(pr);
      }
    };
    handleGroups();
  }, [selectedSchools]);

  useEffect(() => {
    const handleGroups = async () => {
      if (type === "group" && selectedPrograms.length) {
        const response = await apiClientProtected().get(
          `/messages/filter/programs?programs=[${selectedPrograms.map(
            (item) => item
          )}]&in_messages=`
        );
        const pr = response.data.groups.map((item) => {
          item.label = item.name_ka;
          item.value = item.id;
          return item;
        });
        setGroups(pr);
      } else if (type === "school" && selectedPrograms.length) {
        const response = await apiClientProtected().get(
          `/messages/filter/programs?programs=[${selectedPrograms.map(
            (item) => item
          )}]&in_messages=`
        );

        const pr = response.data.flows.map((item) => {
          item.label = item.name;
          item.value = item.id;
          return item;
        });
        setFlows(pr);
      }
    };
    handleGroups();
  }, [selectedPrograms]);

  const handleType = (type) => {
    setSearchString("");
    setType(type);
  };

  const handleReceivers = async (e) => {
    setSearchString(e.target.value);
    if (e.target.value.length > 1) {
      const response = await apiClientProtected().get(
        `/messages/search/receiver?type=${type}&keyword=${e.target.value}`
      );
      setUsers(response.data);
      if (response.data.length) {
        setShowDropdown(true);
      }
      console.log(response);
    } else {
      setUsers([]);
    }
  };

  useOutsideClick(elementRef, () => setShowDropdown(false));

  const handleChange = (e) => {
    if (e.target.name === "attachments") {
      setFieldData({
        ...fieldData,
        [e.target.name]: [
          ...fieldData.attachments,
          ...Object.values(e.target.files),
        ],
      });
      // setFiles(Object.values(e.target.files))
    } else {
      setFieldData({ ...fieldData, [e.target.name]: e.target.value });
    }
  };

  const handleFileDelete = (indx) => {
    const copy = [...fieldData.attachments];
    setFieldData({
      ...fieldData,
      attachments: copy.filter((item, index) => index !== indx),
    });
  };

  const handleEditor = (value, name) => {
    setFieldData({ ...fieldData, [name]: value });
  };

  const handleAdd = (data) => {
    setSelectedUsers([...selectedUsers, data]);
    const copied = [...users];
    setUsers([...copied.filter((item) => item.id !== data.id)]);
    setSearchString("");
  };

  const handleRemove = (id) => {
    const copiedArray = [...selectedUsers];
    const filtered = copiedArray.filter((item) => item.id !== id);
    setSelectedUsers(filtered);
    setUsers([...users, copiedArray.find((item) => item.id === id)]);
  };

  const handleFilterValue = (value) => {
    const { name, arrData } = value;
    if (name === "schools") {
      setSelectedSchools(arrData);
    } else if (name === "programs") {
      setSelectedPrograms(arrData);
    } else if (name === "groups") {
      setSelectedGroups(arrData);
    } else if (name === "flows") {
      setSelectedFlows(arrData);
    }
  };

  const handleFile = () => {
    FileUpload.current.click();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitted(true);
    const fd = new FormData();
    for (let key in fieldData) {
      if (key === "attachments") {
        for (let i = 0; i < fieldData[key].length; i++) {
          fd.append(`attachments[${i}]`, fieldData[key][i]);
        }
      } else {
        fd.append(key, fieldData[key]);
      }
    }

    if (selectedUsers.length) {
      for (let i = 0; i < selectedUsers.length; i++) {
        fd.append(`users[${i}]`, selectedUsers[i].user_id);
      }
    } else if (selectedGroups.length) {
      for (let i = 0; i < selectedGroups.length; i++) {
        fd.append(`student_groups[${i}]`, selectedGroups[i]);
      }
    } else if (
      selectedSchools.length &&
      !selectedPrograms.length &&
      type === "school"
    ) {
      for (let i = 0; i < selectedSchools.length; i++) {
        fd.append(`schools[${i}]`, selectedSchools[i]);
      }
    } else if (
      selectedPrograms.length &&
      !selectedFlows.length &&
      type === "school"
    ) {
      for (let i = 0; i < selectedPrograms.length; i++) {
        fd.append(`programs[${i}]`, selectedPrograms[i]);
      }
    } else if (selectedFlows.length && type === "school") {
      for (let i = 0; i < selectedFlows.length; i++) {
        fd.append(`flows[${i}]`, selectedFlows[i]);
      }
    }
    // /messages/:mainMessageId
    if (formType === "reply") {
      fd.append("main_message_id", messageId);
    }

    try {
      const response = await apiClientProtected().post("/messages/send", fd);
      // console.log(response)

      setIsSubmitted(false);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: locale && langs[locale]["message_sent"],
        text: "",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log(123),
      });

      if (formType === "reply") {
        getMessages();
      }

      setSelectedGroups([]);
      setSelectedFlows([]);
      setSelectedUsers([]);
      setSelectedPrograms([]);
      setSelectedSchools([]);
      setFieldData({
        body: "<p><br></p>",
        title: "",
        attachments: [],
      });
    } catch (err) {
      console.log(err);
      setIsSubmitted(false);
    }
  };

  return (
    <ComposeWrapper>
      {formType !== "reply" && (
        <FormHeader>
          <h2>{locale && langs[locale]["compose"]}</h2>
          <FlexElement>
            {TYPES.map((item, index) => (
              <SentTo
                key={index}
                onClick={() => handleType(item.type)}
                className={`${item.type === type && "active"}`}
              >
                {item.name}
              </SentTo>
            ))}
          </FlexElement>
        </FormHeader>
      )}
      <FormElement onSubmit={handleSubmit}>
        {formType !== "reply" && (
          <>
            <FlexElement ref={elementRef}>
              <label htmlFor="searchString">
                {locale && langs[locale]["to"]}:
              </label>
              <SelectedUsers>
                {selectedUsers.map((item) => (
                  <li key={item.id}>
                    {item.image && <img src={item.image} alt="" />}
                    <span>
                      {item.name || item.first_name} {item.last_name}
                    </span>
                    <MdClose onClick={() => handleRemove(item.id)} />
                  </li>
                ))}
              </SelectedUsers>
              {type === "personal" ? (
                <>
                  <input
                    type="text"
                    name="searchString"
                    id="searchString"
                    value={searchString}
                    onChange={handleReceivers}
                    onClick={() => setShowDropdown(!showDropdown)}
                  />
                  <UserDropDown showDropdown={showDropdown}>
                    <DropdownWrapper>
                      <div>
                        <span>{users.length} Members</span>
                      </div>
                      <UserList>
                        {type === "personal"
                          ? users.map((item) => (
                              <li key={item.id} onClick={() => handleAdd(item)}>
                                <div>
                                  <img
                                    src={
                                      !item.photo
                                        ? "/assets/media/avatars/blank.png"
                                        : process.env.NEXT_PUBLIC_STORAGE +
                                          item.photo
                                    }
                                    alt=""
                                  />
                                </div>
                                <div>
                                  <h4>
                                    {item.first_name} {item.last_name}
                                  </h4>
                                  <span>{item.email}</span>
                                </div>
                              </li>
                            ))
                          : groups.map((item) => (
                              <li key={item.id} onClick={() => handleAdd(item)}>
                                <div style={{ display: "none" }}></div>
                                <div>
                                  <h4>
                                    {item.first_name} {item.last_name}
                                  </h4>
                                </div>
                              </li>
                            ))}
                      </UserList>
                    </DropdownWrapper>
                  </UserDropDown>
                </>
              ) : type === "group" ? (
                <div
                  style={{
                    display: "grid",
                    gap: "8px",
                    gridTemplateColumns: "repeat(3, 1fr)",
                  }}
                >
                  <MultiContainer>
                    <BaseFilterSelect
                      data={schools}
                      name="schools"
                      multiSelect={true}
                      setValue={handleFilterValue}
                      placeholder={"არჩევა"}
                    />
                  </MultiContainer>
                  {selectedSchools.length > 0 && (
                    <MultiContainer>
                      <BaseFilterSelect
                        data={programs}
                        name="programs"
                        multiSelect={true}
                        setValue={handleFilterValue}
                        placeholder={"არჩევა"}
                      />
                    </MultiContainer>
                  )}
                  {selectedPrograms.length > 0 && (
                    <MultiContainer>
                      <BaseFilterSelect
                        data={groups}
                        name="groups"
                        multiSelect={true}
                        setValue={handleFilterValue}
                        placeholder={"არჩევა"}
                      />
                    </MultiContainer>
                  )}
                </div>
              ) : (
                <div
                  style={{
                    display: "grid",
                    gap: "8px",
                    gridTemplateColumns: "repeat(3, 1fr)",
                  }}
                >
                  <MultiContainer>
                    <BaseFilterSelect
                      data={schools}
                      name="schools"
                      multiSelect={true}
                      setValue={handleFilterValue}
                      placeholder={"არჩევა"}
                    />
                  </MultiContainer>
                  {selectedSchools.length > 0 && (
                    <MultiContainer>
                      <BaseFilterSelect
                        data={programs}
                        name="programs"
                        multiSelect={true}
                        setValue={handleFilterValue}
                        placeholder={"არჩევა"}
                      />
                    </MultiContainer>
                  )}
                  {selectedPrograms.length > 0 && (
                    <BaseFilterSelect
                      data={flows}
                      name="flows"
                      multiSelect={true}
                      setValue={handleFilterValue}
                      placeholder={"არჩევა"}
                    />
                  )}
                </div>
              )}
            </FlexElement>
          </>
        )}
        {formType !== "reply" && (
          <SubjectElement>
            <input
              type="text"
              name="title"
              id="title"
              value={fieldData.title}
              placeholder={locale && langs[locale]["title"]}
              onChange={handleChange}
            />
          </SubjectElement>
        )}
        <ReactQuill
          theme="snow"
          placeholder={locale && langs[locale]["type_text"]}
          id="body"
          value={fieldData.body}
          onChange={(value) => handleEditor(value, "body")}
        />
        <input
          type="file"
          ref={FileUpload}
          name="attachments"
          multiple
          value={fieldData.file}
          style={{ display: "none" }}
          accept=".jpg, .jpeg, .ppt, .pptx, .zip, .png, .gif, .bmp, .doc, .docx, .pdf, .xlsx, .xls, .txt"
          onChange={handleChange}
        />
        <FormController>
          <button
            className="btn btn-primary"
            style={{ height: "40px", width: "120px" }}
            type="submit"
          >
            {isSubmitted ? <ButtonLoader /> : locale && langs[locale]["send"]}
          </button>
          <span
            className="btn btn-icon btn-sm btn-clean btn-active-light-primary me-2 dz-clickable"
            onClick={handleFile}
          >
            <span className="svg-icon svg-icon-2 m-0">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.3"
                  d="M4.425 20.525C2.525 18.625 2.525 15.525 4.425 13.525L14.825 3.125C16.325 1.625 18.825 1.625 20.425 3.125C20.825 3.525 20.825 4.12502 20.425 4.52502C20.025 4.92502 19.425 4.92502 19.025 4.52502C18.225 3.72502 17.025 3.72502 16.225 4.52502L5.82499 14.925C4.62499 16.125 4.62499 17.925 5.82499 19.125C7.02499 20.325 8.82501 20.325 10.025 19.125L18.425 10.725C18.825 10.325 19.425 10.325 19.825 10.725C20.225 11.125 20.225 11.725 19.825 12.125L11.425 20.525C9.525 22.425 6.425 22.425 4.425 20.525Z"
                  fill="currentColor"
                ></path>
                <path
                  d="M9.32499 15.625C8.12499 14.425 8.12499 12.625 9.32499 11.425L14.225 6.52498C14.625 6.12498 15.225 6.12498 15.625 6.52498C16.025 6.92498 16.025 7.525 15.625 7.925L10.725 12.8249C10.325 13.2249 10.325 13.8249 10.725 14.2249C11.125 14.6249 11.725 14.6249 12.125 14.2249L19.125 7.22493C19.525 6.82493 19.725 6.425 19.725 5.925C19.725 5.325 19.525 4.825 19.125 4.425C18.725 4.025 18.725 3.42498 19.125 3.02498C19.525 2.62498 20.125 2.62498 20.525 3.02498C21.325 3.82498 21.725 4.825 21.725 5.925C21.725 6.925 21.325 7.82498 20.525 8.52498L13.525 15.525C12.325 16.725 10.525 16.725 9.32499 15.625Z"
                  fill="currentColor"
                ></path>
              </svg>
            </span>
          </span>
          <FilesList>
            {fieldData.attachments?.map((item, index) => (
              <li key={index}>
                <span>{item.name}</span>
                <MdClose onClick={() => handleFileDelete(index)} />
              </li>
            ))}
          </FilesList>
        </FormController>
        {success && (
          <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
        )}
      </FormElement>
    </ComposeWrapper>
  );
};

export default InboxForm;

const FormElement = styled.form`
  border: 1px solid #f4f4f4;
  border-radius: 6px;
`;

const FlexElement = styled.div`
  position: relative;
  display: flex;
  gap: 15px;
  align-items: center;
  padding-left: 30px;
  border-bottom: 1px solid #f4f4f4;
  label {
    font-weight: 600;
  }
  input {
    padding: 1rem;
    flex-grow: 1;
  }
`;

const SubjectElement = styled.div`
  padding-left: 30px;
  border-bottom: 1px solid #f4f4f4;
  input {
    padding: 1rem;
    width: 50%;
    padding-left: 0;
    &::placeholder {
      color: #a1a5b7;
    }
    @media (max-width: 1280px) {
      width: 100%;
    }
  }
`;

const FormController = styled.div`
  padding: 1rem 30px;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  button {
    letter-spacing: 1px;
  }
`;

const FilesList = styled.ul`
  display: flex;
  align-items: center;
  gap: 4px;
  li {
    border: 1px solid #c2d6ec;
    border-radius: 6px;
    box-shadow: 1px 1px 4px rgb(0 0 0 / 10%);
    padding: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;

const UserDropDown = styled.div`
  position: absolute;
  width: 100%;
  display: block;
  top: 100%;
  left: 0;
  display: block;
  max-height: ${({ showDropdown }) => (showDropdown ? "400px" : "0")};
  background: #fff;
  transition: all 300ms;
  z-index: 1;
  border-radius: 0 0 0.475rem 0.475rem;
  overflow: auto;
  box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  /* padding: 0.75rem 0; */
`;

const DropdownWrapper = styled.div`
  padding: 0.75rem 0;
  & > div {
    width: 100%;
    padding: 1rem 1.5rem;
    color: #5e6278;

    cursor: pointer;
    &:hover {
      background: #f4f6fa;
      color: #009ef7;
    }
    p {
      font-weight: 700;
    }
  }
`;

const ComposeWrapper = styled.div`
  header {
    padding-left: 30px;
    border-bottom: 1px solid #f4f4f4;
    h2 {
      font-size: 1.75rem;
      padding-top: 1.75rem;
      padding-bottom: 1.75rem;
    }
  }
`;
const UserList = styled.ul`
  li {
    display: flex;
    gap: 13px;
    align-items: center;
    padding: 0.75rem 1.5rem;
    /* padding-bottom: 0; */
    cursor: pointer;
    &:hover {
      background: #f4f6fa;
      color: #009ef7;
    }
    &:hover h4,
    &:hover span {
      background: #f4f6fa;
      color: #009ef7;
    }
  }
  h4 {
    font-weight: 700;
    font-size: 13px;
    color: #5e6278;
  }
  span {
    font-size: 13px;
    color: #5e6278;
  }
  div:nth-child(1) {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
`;

const SelectedUsers = styled.ul`
  display: flex;
  gap: 4px;
  width: auto;
  li {
    padding: 0.4rem 0.8rem;
    border-radius: 0.475rem;
    border: 1px solid #0095e8;
    background-color: #fff;
    box-shadow: 1px 1px 6px 1px rgba(0, 0, 0, 0.1);
    margin: 0;
    line-height: 1;
    white-space: pre-wrap;
    text-overflow: ellipsis;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    max-width: fit-content;
  }
  span {
    color: #5e6278;
  }
  svg {
    font-weight: 700;
    cursor: pointer;
  }
  img {
    width: 26px;
    height: 26px;
    border-radius: 50%;
  }
`;
const SentTo = styled.div`
  margin: 4px 0;
  background: #fff;
  border: 1px solid #c2d6ec;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.1);
  color: #666;
  transition: all 300ms;
  &.active {
    background-color: #0095e8;
    color: #fff;
  }
  &:hover {
    background: #0095e8;
    color: #fff;
  }
`;
const MultiContainer = styled.div`
  width: 200px;
`;

const FormHeader = styled.header`
  display: flex;
  gap: 1rem;
  align-items: center;
`;
