import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/router";
import InboxForm from "./content/InboxForm";
import styled from "styled-components";
import { MdChevronLeft, MdPeopleOutline } from "react-icons/md";
import useOutsideClick from "./../custom_hooks/useOutsideClick";
import { langs } from "../locale";
import { useLocaleContext } from "../context/LocaleContext";
import apiClientProtected from "../../helpers/apiClient";
import { getBirthDate } from "./../../helpers/funcs";
import { HiOutlineMinus } from "react-icons/hi2";
import Link from "next/link";
import Image from "next/image";

const Reply = ({ messageId }) => {
  const router = useRouter();
  const { locale } = useLocaleContext();
  const elementRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [mainMessage, setMainMessage] = useState({});
  const [openDropdown, setOpenDropdown] = useState(false);
  const [participants, setParticipants] = useState([]);
  const actionBtn = useRef(null);

  useOutsideClick(elementRef, () => setIsOpen(false));
  useOutsideClick(actionBtn, () => setOpenDropdown(false));

  useEffect(() => {
    getMessages();
  }, []);

  const getMessages = async () => {
    const response = await apiClientProtected().get(`/messages/${messageId}`);
    setMessage(response.data.messages);
    if (true) {
      const addresses = response.data.messages.addresses;
      console.log(addresses);
      const newAddresses = addresses
        ?.map((item) => ({
          id: item.id,
          name: item.user.name.split(" ").reverse().join(" "),
          last_name: item.user.name.split(" ")[1],
          viewed_at: item.viewed_at,
        }))
        .sort((a, b) => a.name.localeCompare(b.name));
      setParticipants(newAddresses);
    }
    setMainMessage(response.data.messages.replies[0]);
  };

  return (
    <div className="flex-lg-row-fluid ">
      <div className="card">
        <div className="card-body">
          <div className="d-flex flex-wrap gap-2 justify-content-between mb-8">
            <div className="d-flex align-items-center flex-wrap gap-2">
              <span className="pointer" onClick={() => router.back()}>
                <MdChevronLeft size={24} />
              </span>
              <h2 className="fw-semibold me-3 my-1">{message.title}</h2>
            </div>
          </div>
          <div data-kt-inbox-message="message_wrapper">
            <div
              className="d-flex flex-wrap gap-2 flex-stack cursor-pointer"
              data-kt-inbox-message="header"
            >
              <div className="d-flex align-items-center">
                <div className="symbol symbol-50 me-4">
                  <span
                    className="symbol-label"
                    style={{
                      backgroundImage: `${
                        message.author && message.author.photo
                          ? "url(/assets/media/avatars/300-18.jpg)"
                          : "url(/icons/user.png)"
                      } `,
                    }}
                  ></span>
                </div>
                <div className="pe-5">
                  <div className="d-flex align-items-center flex-wrap gap-1">
                    <a
                      href="#"
                      className="text-dark text-hover-primary font-bold"
                    >
                      {message.author && message.author.name}
                    </a>
                  </div>
                  {message.created_at && (
                    <span className="fw-semibold text-muted text-end me-3">
                      {message.created_at.slice(0, 10)}{" "}
                      {message.created_at.split(" ")[1].slice(0, 5)}
                    </span>
                  )}
                  <div
                    className="text-muted fw-semibold mw-450px d-none"
                    data-kt-inbox-message="preview"
                  >
                    With resrpect, i must disagree with Mr.Zinsser. We all know
                    the most part of important part....
                  </div>
                </div>
              </div>
              <div className="">
                <DropDownSection openDropdown={openDropdown} ref={actionBtn}>
                  <button
                    className="dropdown-button"
                    onClick={() => setOpenDropdown(!openDropdown)}
                  >
                    <MdPeopleOutline size={18} />
                    {locale && langs[locale]["group"]}
                  </button>
                  <div
                    className={`${openDropdown ? "drop-effect" : ""} dropdown`}
                  >
                    <div className="dropdown-window">
                      <ul className="dropdown-list">
                        {participants?.map((item, index) => (
                          <li key={index} className="dopdown-item">
                            <div className="dropdown-inner-box">
                              <span title={item.viewed_at}>
                                {index + 1}. {item.name}
                              </span>
                              {item.viewed_at ? (
                                <div className="user-check">
                                  <MdCheck color="#fff" />
                                </div>
                              ) : (
                                <div
                                  className="user-check"
                                  style={{ background: "rgb(210 214 225)" }}
                                >
                                  <HiOutlineMinus color="#fff" />
                                </div>
                              )}
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </DropDownSection>
              </div>
            </div>
            <div className="collapse fade show" data-kt-inbox-message="message">
              <div
                className="py-5 body-div"
                id="bd"
                dangerouslySetInnerHTML={{ __html: message.body }}
              >
                {/* <p className="mb-4">Hi Bob,</p>
                <p className="mb-4">With resrpect, i must disagree with Mr.Zinsser. We all know the most part of important part of any article is the title.Without a compelleing title, your reader won't even get to the first sentence.After the title, however, the first few sentences of your article are certainly the most important part.</p>
                <p className="mb-4">Jornalists call this critical, introductory section the "Lede," and when bridge properly executed, it's the that carries your reader from an headine try at attention-grabbing to the body of your blog post, if you want to get it right on of these 10 clever ways to omen your next blog posr with a bang</p>
                <p className="mb-4">Best regards,</p>
                <p className="mb-0">Jason Muller</p> */}
              </div>
              <div>
                {message.attachments && message.attachments.length ? (
                  <div style={{ fontWeight: "bold", margin: "1rem 0 0.5rem" }}>
                    {message.attachments.length}{" "}
                    {locale && langs[locale]["attachment"]}{" "}
                    {message.attachments.length > 1 && locale === "en" && "s"}
                  </div>
                ) : null}
                <ImageWrapper>
                  {message.attachments &&
                    message.attachments.map((item, index) => (
                      <AttachedFile key={index}>
                        <Link
                          href={`${process.env.NEXT_PUBLIC_STORAGE}/${item.filename}`}
                        >
                          <a>
                            <div className="attachment">
                              <Image
                                src="/assets/media/pdf-icon.svg"
                                width={30}
                                height={30}
                              />

                              {locale && langs[locale]["download"]}
                            </div>
                          </a>
                        </Link>
                      </AttachedFile>
                    ))}
                </ImageWrapper>
              </div>
            </div>
          </div>
          <div className="separator my-6"></div>
          {mainMessage && Object.entries(mainMessage).length && (
            <div data-kt-inbox-message="message_wrapper">
              {message.replies.map((item, index) => (
                <Message isCollapsed={isCollapsed} key={index}>
                  <div className="border-bottom p-4">
                    <div className="d-flex justify-content-between">
                      <div className="d-flex align-items-center ">
                        <div className="symbol symbol-50 me-4">
                          <span
                            className="symbol-label"
                            style={{
                              backgroundImage: `${
                                !item.author.profile_photo_path
                                  ? "url(/icons/user.png)"
                                  : `${item.author.profile_photo_path}`
                              }`,
                            }}
                          ></span>
                        </div>
                        <div>
                          <div className="font-bold">{item.author.name}</div>
                          <span className="text-muted">
                            {item.author.email}
                          </span>
                        </div>
                      </div>
                      <div className="d-flex align-items-center flex-wrap gap-2">
                        <span className="fw-semibold text-muted text-end me-3">
                          {item.created_at.slice(0, 10)}{" "}
                          {item.created_at.split(" ")[1].slice(0, 5)}
                        </span>
                      </div>
                    </div>
                    <div
                      className="py-5 body-div"
                      dangerouslySetInnerHTML={{ __html: item.body }}
                    ></div>
                    {item.attachments && item.attachments.length ? (
                      <div
                        style={{
                          fontWeight: "bold",
                          margin: "1rem 0 0.5rem",
                        }}
                      >
                        {item.attachments.length}{" "}
                        {locale && langs[locale]["attachment"]}{" "}
                        {item.attachments.length > 1 && locale === "en" && "s"}
                      </div>
                    ) : null}
                    <ImageWrapper>
                      {item.attachments &&
                        item.attachments.map((attachment, index) => (
                          <AttachedFile key={index}>
                            <Link
                              href={`${process.env.NEXT_PUBLIC_STORAGE}/${attachment.filename}`}
                            >
                              <a>
                                <div className="attachment">
                                  <Image
                                    src="/assets/media/pdf-icon.svg"
                                    width={30}
                                    height={30}
                                  />

                                  {locale && langs[locale]["download"]}
                                </div>
                              </a>
                            </Link>
                          </AttachedFile>
                        ))}
                    </ImageWrapper>
                  </div>
                </Message>
              ))}
            </div>
          )}

          <div className="mb-8"></div>
          {/* <div className="separator my-6"></div> */}
          <InboxForm
            formType={"reply"}
            messageId={messageId}
            getMessages={getMessages}
          />
        </div>
      </div>
    </div>
  );
};

export default Reply;

const Message = styled.div`
  overflow: hidden;
  transition: all 300ms;
  /* border-bottom: 1px solid #eff2f5; */
  border-radius: 1rem;
  overflow: hidden;
  .body-div {
    p {
      margin-bottom: 1rem;
    }
    p:last-child {
      margin-bottom: 0;
    }
  }
  div {
    :hover {
      background: #f8fdff;
    }
  }
`;

const ImageWrapper = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  div {
    width: 150px;
    img {
      width: 100%;
    }
  }
`;

const AttachedFile = styled.div`
  border: 1px solid #eef3ff;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.1);
  a {
    padding: 6px 10px;
  }
  span {
    border-radius: 0;
  }
  .attachment {
    display: flex;
    align-items: center;
    gap: 0 1rem;
  }
`;

const DropDownSection = styled.div`
  position: relative;
  .dropdown-button {
    border-radius: 8px;
    padding: 8px 16px;
    background: #eef3ff;
    letter-spacing: 1px;
    color: #7b8299;
    display: flex;
    border: 1px solid #ddd;
    gap: 8px;
    align-items: center;
    :hover {
      background: #dee7ff;
    }
  }
  div.dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    z-index: 5;
    border: 1px solid #eef3ff;
    background: #fff;
    transform: scale(0);
    transform-origin: 20px 20px;
    padding: 1rem;
    max-width: none;
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    width: max-content;
    :before {
      content: "";
      position: absolute;
      top: -8px;
      right: 16px;
      width: 12px;
      height: 12px;
      border-top: 1px solid #eef3ff;
      border-left: 1px solid #eef3ff;
      transform: rotate(45deg);
      display: block;
      background: #fff;
    }
  }
  div.dropdown-window {
    max-height: 300px;
    overflow-y: auto;
  }
  div.drop-effect {
    transform: scale(1);
    transition: all 100ms;
  }
  .dopdown-item {
    margin-bottom: 1rem;
    :last-child {
      margin-bottom: 0;
    }
  }
  .dropdown-inner-box {
    display: flex;
    flex-direction: row;
    gap: 2.5rem !important;
    align-items: center;
    justify-content: space-between;
  }
  .user-check {
    width: 20px;
    height: 20px;
    background: #00a83f;
    border: 1px solid #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
  }
`;
