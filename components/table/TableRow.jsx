import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useUserContext } from "../context/UserContext";
import apiClientProtected from "../../helpers/apiClient";
import Sweet<PERSON>lert2 from "react-sweetalert2";

import useOutsideClick from "../custom_hooks/useOutsideClick";
import useEscapeKey from "../custom_hooks/useEscapeKey";
import Link from "next/link";
import { HiOutlineEye } from "react-icons/hi";
import {
  MdOutlineModeEdit,
  MdOutlineDelete,
  MdFileCopy,
  MdPictureAsPdf,
  MdOutlinePassword,
  MdOutlinePeopleOutline,
} from "react-icons/md";
import { FaWallet } from "react-icons/fa";
import { GrBook, GrDocumentVerified } from "react-icons/gr";

import { langs } from "./../locale";
import { useLocaleContext } from "../context/LocaleContext";
import Modal from "../ui/Modal";
import SubmitLoader from "../ui/SubmitLoader";
import EditForm from "../forms/EditForm";
import { useTableContext } from "../context/TableContext";
import DynamicRow from "../table_rows/DynamicRow";
import SurveysList from "../forms/surveys/SurveysList";
import StudentEditForm from "../forms/students/StudentEditForm";
import LecturerEditForm from "../forms/lecturers/LecturerEditForm";
import LibraryEditForm from "../forms/library/LibraryEditForm";
import StatementEditForm from "../forms/StatementEditForm";
import RoleEditForm from "../forms/roles/RolesEditForm";
import PassRecoveryForm from "../forms/PassRecoveryForm";
import AdminEditForm from "../forms/administration/AdminEditForm";
import AuditoriumEditForm from "../forms/auditoriums/AuditoriumEditForm";
import NewsEditForm from "../forms/news/NewsEditForm";
import EdocEditForm from "../forms/edoc/EdocEditForm";
import FullModal from "../../components/ui/FullModal";
import AdminForm from "../../components/forms/hr/AdminForm";
import InvitedForm from "../../components/forms/hr/InvitedForm";
import AcademicForm from "../forms/hr/AcademicForm";
import UserView from "../forms/hr/UserView";
import StudentCard from "./../applicants/StudentCard";
import CurrentSubjects from "../applicants/CurrentSubjects";
import PassedSubjects from "../applicants/PassedSubjects";
import BachelorCard from "./../applicants/BachelorCard";
import MasterCard from "./../applicants/MasterCard";
import DoctorCard from "./../applicants/DoctorCard";
import TrainingCard from "./../applicants/TrainingCard";
import ProfessionCard from "./../applicants/ProfessionCard";
import EdocTable from "../forms/edoc/EdocTable";
import SurveyEditForm from "../forms/surveys/SurveyEditForm";
// import StudentPayments from "../applicants/StudentPayments";
import FinanceReport from "../student/finance/FinanceReport";
import SurveyView from "../forms/surveys/SurveyView";
import UsersList from "./../modal/UsersList";
import { FaSignInAlt } from "react-icons/fa";
import { MdFileDownload } from 'react-icons/md';
import Cookies from "js-cookie";
import {useRouter} from "next/router";
import {isNull} from "lodash";

function TableRow({ data, id, fields, fetchLink, checkAll, collectChecked }) {
  const { locale } = useLocaleContext();
  const { user } = useUserContext();

  const [openAction, setOpenAction] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showPassRecoveryForm, setShowPassRecoveryForm] = useState(false);
  const [showPaymentsForm, setShowPaymentsForm] = useState(false);
  const [showEdoc, setShowEdoc] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [checked, setChecked] = useState(checkAll);
  const [swalProps, setSwalProps] = useState({});
  const [userDeleted, setUserDeleted] = useState(false);
  const [modalTitle, setModalTitle] = useState("");
  const [showUsersList, setShowUsersList] = useState(false);
  const [showSurveys, setShowSurveys] = useState(false);

  const actionBtn = useRef();
  useOutsideClick(actionBtn, () => setOpenAction(false));

  const {
    handleDataDeletion,
    handleDataEdit,
    pageInfo,
    modalType,
    setModalType,
    dataId,
    setDataId,
    setAlertMessage,
  } = useTableContext();

  const handleModalClose = () => {
    setOpenModal(false);
    setShowEditForm(false);
    setShowPassRecoveryForm(false);
  };
  useEscapeKey(handleModalClose);

  useEffect(() => {
    if (data) {
      data.name_ka && setModalTitle(data.name_ka);
      data.first_name &&
        setModalTitle(data.first_name + " " + (data.last_name || ""));
      data.name && setModalTitle(data.name + " " + (data.surname || ""));
    }
  }, [data]);

  const handleExport = async (id) => {
    try {
      const response = await apiClientProtected().get(`/edoc/export/${id}`);

      const fileResponse = await apiClientProtected().get(
        `/download-pdf?filename=${response.data.filename}`,
        {
          responseType: "arraybuffer",
        }
      );

      const blob = new Blob([fileResponse.data], { type: "application/pdf" });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = response.data.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.log(err);
    }
  };

  const handleDataDelete = (id) => {
    setIsSubmitting(true);
    // console.log(fetchLink, id); return
    apiClientProtected()
      .delete(`${fetchLink}/${id}`)
      .then((res) => {
        if (res.status === 204) {
          setIsSubmitting(false);
          handleDataDeletion(id);
          setOpenModal(false);
          setUserDeleted(true);
          setSwalProps({
            show: true,
            title: "წაშლილია!",
            text: "წარმატებით წაიშალა",
            icon: "success",
            confirmButtonColor: "#009ef7",
          });
        }
      })
      .catch((err) => err && setIsSubmitting(false));
  };

  const handleGpa = async (id) => {
    const fd = new FormData();
    fd.append("program_id", id);
    try {
      const response = await apiClientProtected().post(
        "/administration/student-gpa-calculate",
        fd
      );
      setAlertMessage({
        isOpen: true,
        title: response.data.message,
      });
    } catch (err) {
      console.log(err);
    }
  };

  const onsubmit = (data) => {
    collectChecked(data);
  };

  const adminLogin = async (userId) => {
    const adminApi = apiClientProtected();
    const formData = new FormData();
    formData.append("user_id", userId);
    const response = await adminApi.post(`/admin/student-auth`, formData);
    const { token, user } = response.data;
    localStorage.setItem('user', JSON.stringify(user))
    Cookies.set("token", token, { expires: 7 });
    if (user.user_type === 3) {
      window.location.href = '/student';
    }else{
      window.location.href = '/lecturer';
    }
  };

  const [showDiplomaLangModal, setShowDiplomaLangModal] = useState(false);
  const [diplomaStudentId, setDiplomaStudentId] = useState(null);
  const [diplomaStudentData, setDiplomaStudentData] = useState(null);

  const handleDiplomaDownload = (id) => {
    setDiplomaStudentId(id);
    setDiplomaStudentData(data);
    setShowDiplomaLangModal(true);
  };

  const downloadDiplomaWithLang = async (lang) => {
    try {
      // დაცული API კლიენტის გამოყენება
      const adminApi = apiClientProtected();

      // მოთხოვნა ბექენდზე ბინარული ფაილის მისაღებად
      const formData = new FormData();
      formData.append('student_id', diplomaStudentId);

      const response = await adminApi.post(`/administration/student-diploma-download/${lang}`, formData, {
        responseType: 'blob', // მნიშვნელოვანია ბინარული პასუხისთვის
      });

      // Blob-ის მიღება პასუხიდან
      const blob = response.data;

      // URL-ის შექმნა Blob-ისთვის
      const url = window.URL.createObjectURL(blob);

      // ფაილის გადმოწერისთვის დროებითი ლინკის შექმნა
      const link = document.createElement('a');
      link.href = url;

      // ფაილის სახელი უნდა იყოს სტუდენტის სახელი-გვარი-diploma-supplement-ენა
      // სახელი და გვარი ავიღებთ მონაცემებიდან
      link.download = `${diplomaStudentData.name}-${diplomaStudentData.surname}-diploma-supplement-${lang}.docx`;
      document.body.appendChild(link);
      link.click();

      // გაწმენდა
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      setShowDiplomaLangModal(false);
    } catch (error) {
      console.error('შეცდომა დიპლომის გადმოწერისას:', error);
      alert('დიპლომის გადმოწერა ვერ მოხერხდა, გთხოვთ სცადოთ ხელახლა.');
      setShowDiplomaLangModal(false);
    }
  };

  return (
    <tr>
      <td>
        <div className="form-check form-check-sm form-check-custom form-check-solid">
          {/* <input
                        className="form-check-input widget-9-check"
                        type="checkbox"
                        value="1"
                        // checked={checkAll}
                        {...register('row_checkbox_' + id)}
                        onClick={handleSubmit(onsubmit)}
                    /> */}
          {id}
        </div>
      </td>

      <DynamicRow data={data} />

      {pageInfo.routeName !== "minor-logs" && (
        <td className="table__last__row">
          {/* text end comment */}
          {pageInfo.routeName !== "academic" &&
          pageInfo.routeName !== "invited" &&
          pageInfo.routeName !== "administration" ? (
          <div
            className="btn btn-light btn-active-light-primary btn-sm d-flex align-items-center"
            onClick={() => setOpenAction(!openAction)}
            ref={actionBtn}
            style={{ maxWidth: "fit-content", marginLeft: "auto" }}
          >
            {locale && langs[locale]["action"]}
            <span className="svg-icon svg-icon-5 m-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                style={{ transform: `rotate(${openAction ? "180deg" : 0})` }}
              >
                <path
                  d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                  fill="currentColor"
                ></path>
              </svg>
            </span>
          </div>
        ) : (
          <div className="d-flex gap-6 justify-content-end">
            <span className="pointer">
              <HiOutlineEye
                size={18}
                onClick={() => {
                  setOpenModal(true);
                  setShowEditForm(true);
                  setModalType("show");
                  setDataId(data.id);
                }}
              />
            </span>
            <span className="pointer">
              <MdOutlineModeEdit
                size={18}
                onClick={() => {
                  setOpenModal(true);
                  setShowEditForm(true);
                  setModalType("edit");
                  setDataId(data.id);
                }}
              />
            </span>
          </div>
        )}

        <div style={{ position: "relative" }}>
          <AnimatePresence>
            {openAction && (
              <>
                <motion.div
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 15 }}
                  exit={{ opacity: 0, y: 40 }}
                  transition={{ type: "spring", damping: 15, duration: 0.2 }}
                  className="table__last__row__actions"
                >
                  <div
                    className={`
                      menu menu-sub
                      menu-sub-dropdown
                      menu-column menu-rounded
                      menu-gray-600 menu-state-bg-light-primary
                      fw-bold fs-7 py-4 show
                      ${
                        ["students", "lecturers", "administrations"].includes(
                          pageInfo.routeName
                        )
                          ? "min-w-200px"
                          : "w-175px"
                      }
                    `}
                  >
                    {["students"].includes(pageInfo.routeName) &&
                      user.permissions.includes(
                        `${pageInfo.routeName}.update`
                      ) && (
                            <>
                              <div className="menu-item px-3">
                                <div
                                    className="menu-link px-3"
                                    onClick={() => {
                                      setDataId(data.id);
                                      setModalType(`${pageInfo.routeName}-subjects`);
                                      setTimeout(() => setOpenModal(true), 300);
                                    }}
                                >
                                  <GrBook/>
                                  {locale && langs[locale]["current_subjects"]}
                                </div>
                              </div>
                              <div className="menu-item px-3">
                                <div
                                    className="menu-link px-3"
                                    onClick={() => {
                                      setDataId(data.id);
                                      setModalType(`past-subjects`);
                                      setTimeout(() => setOpenModal(true), 300);
                                    }}
                                >
                                  <GrBook/>
                                  {locale && langs[locale]["past_subjects"]}
                                </div>
                              </div>
                            </>
                        )}
                    {["programs"].includes(pageInfo.routeName) && (
                        <div className="menu-item px-3">
                          <div
                              className="menu-link px-3"
                              onClick={() => handleGpa(data.id)}
                          >
                            <MdFileCopy/>
                            GPA
                        </div>
                      </div>
                    )}

                    {["students", "lecturers"].includes(pageInfo.routeName) && (
                        <div className="menu-item px-3">
                          <div
                              className="menu-link px-3"
                              onClick={() => adminLogin(data.user_id)}
                          >
                            <FaSignInAlt/>
                            შესვლა
                          </div>
                        </div>
                    )}
                    {["students"].includes(pageInfo.routeName) && data.status_id === 6 && (
                        <div className="menu-item px-3">
                          <div
                              className="menu-link px-3"
                              onClick={() => handleDiplomaDownload(data.id)} // ფუნქცია დიპლომის გადმოსაწერად
                          ><MdFileDownload size={17} />
                           დიპლომის დანართი
                          </div>
                        </div>
                    )}

                    {["students"].includes(pageInfo.routeName) && (
                        <>
                          <div className="menu-item px-3">
                            <div
                                className="menu-link px-3"
                                onClick={() => {
                                  setOpenModal(true);
                                  setModalType("");
                                  setShowEdoc(true);
                            }}
                          >
                            <MdFileCopy />
                            {locale && langs[locale]["prepare_e_doc"]}
                          </div>
                        </div>
                        <div className="menu-item px-3">
                          <div
                            className="menu-link px-3"
                            onClick={() => {
                              setOpenModal(true);
                              setModalType("");
                              setShowPaymentsForm(true);
                            }}
                          >
                            <FaWallet />
                            {locale && langs[locale]["finances"]}
                          </div>
                        </div>
                      </>
                    )}
                    {[
                      "surveys",
                      "bachelor",
                      "master",
                      "phd",
                      "tcc",
                      "hse",
                      "students",
                    ].includes(pageInfo.routeName) && (
                      <div className="menu-item px-3">
                        <div
                          className="menu-link px-3"
                          onClick={() => {
                            setDataId(data.id);
                            setModalType(`${pageInfo.routeName}-view`);
                            setTimeout(() => setOpenModal(true), 300);
                          }}
                        >
                          <HiOutlineEye />
                          {locale && langs[locale]["view"]}
                        </div>
                      </div>
                    )}
                    {[
                      "bachelor",
                      "master",
                      "phd",
                      "tcc",
                      "hse",
                    ].includes(pageInfo.routeName) && (
                      <div className="menu-item px-3">
                        <Link
                          href={`https://api.portal.gipa.ge/student-agreement/${data.id}`}
                        >
                          <a className="menu-link px-3" target="_blank">
                            <GrDocumentVerified />
                            {locale && langs[locale]["agreement"]}
                          </a>
                        </Link>
                      </div>
                    )}
                    {[
                      "students",
                    ].includes(pageInfo.routeName) && (
                        <div className="menu-item px-3">
                          <Link
                              href={`https://api.portal.gipa.ge/student-agreement/enrolled/${data.id}`}
                          >
                            <a className="menu-link px-3" target="_blank">
                              <GrDocumentVerified />
                              {locale && langs[locale]["agreement"]}
                            </a>
                          </Link>
                        </div>
                    )}
                    {pageInfo.routeName === "journal" && (
                      <div className="menu-item px-3">
                        <div
                          onClick={() => {
                            setOpenModal(true);
                            setShowSurveys(true);
                          }}
                        >
                          <span className="menu-link px-3">
                            <MdPictureAsPdf />
                            {locale && langs[locale]["surveys"]}
                          </span>
                        </div>
                      </div>
                    )}
                    {pageInfo.routeName === "library-lmb" ? (
                      <div className="menu-item px-3">
                        <Link href={`/admin/library-lmb/edit/${data.id}`}>
                          <a className="menu-link px-3">
                            <MdOutlineModeEdit />
                            {locale && langs[locale]["edit"]}
                          </a>
                        </Link>
                      </div>
                    ) : pageInfo.routeName === "edoc-inbox" ||
                      pageInfo.routeName === "edoc-sent" ? (
                      <div className="menu-item px-3">
                        <Link href={`/admin/edoc/edit/${data.id}`}>
                          <a className="menu-link px-3">
                            <HiOutlineEye />
                            {locale && langs[locale]["view"]}
                          </a>
                        </Link>
                        <Link href={`/admin/edoc/edit/${data.id}`}>
                          <a className="menu-link px-3">
                            <MdOutlineModeEdit />
                            {pageInfo.routeName === "edoc-inbox"
                              ? "მომზადება"
                              : "რედაქტირება"}
                          </a>
                        </Link>
                        <div onClick={() => handleExport(data.id)}>
                          <span className="menu-link px-3">
                            <MdPictureAsPdf />
                            {locale && langs[locale]["download"]}
                          </span>
                        </div>
                      </div>
                    ) : !["bachelor", "master", "phd", "tcc", "hse"].includes(
                        pageInfo.routeName
                      ) &&
                      user.permissions.includes(
                        `${pageInfo.routeName}.update`
                      ) ? (
                      <div
                        className="menu-item px-3"
                        onClick={() => {
                          setOpenModal(true);
                          setShowEditForm(true);
                          setDataId(data.id);
                          if (pageInfo.routeName === "surveys") {
                            setModalType("surveys");
                            console.log("Rammstein sonne sonne");
                          } else {
                            setModalType("");
                          }
                        }}
                      >
                        <div className="menu-link px-3">
                          <MdOutlineModeEdit />
                          {locale && langs[locale]["edit"]}
                        </div>
                      </div>
                    ) : null}

                    {user.permissions.includes(
                      `${pageInfo.routeName}.destroy`
                    ) && (
                      <div className="menu-item px-3">
                        <div
                          className="menu-link px-3"
                          onClick={() => {
                            setOpenModal(true);
                            setUserDeleted(false);
                            setModalType("delete");
                          }}
                        >
                          <MdOutlineDelete />
                          {locale && langs[locale]["delete"]}
                        </div>
                      </div>
                    )}
                    {["students", "lecturers", "administrations"].includes(
                      pageInfo.routeName
                    ) &&
                      user.permissions.includes(
                        `${pageInfo.routeName}.update`
                      ) && (
                        <div className="menu-item px-3">
                          <div
                            className="menu-link px-3"
                            onClick={() => {
                              setOpenModal(true);
                              setModalType("");
                              setShowPassRecoveryForm(true);
                            }}
                          >
                            <MdOutlinePassword />
                            {locale && langs[locale]["change_password"]}
                          </div>
                        </div>
                      )}

                    {pageInfo.routeName === "roles" && (
                      <div className="menu-item px-3">
                        <div
                          className="menu-link px-3"
                          onClick={() => {
                            setOpenModal(true);
                            setShowUsersList(true);
                            // setUserDeleted(false);
                          }}
                        >
                          <MdOutlinePeopleOutline />
                          მომხმარებლები
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>
        </div>

        {["surveys", "bachelor", "master", "phd", "tcc", "hse"].includes(
          pageInfo.routeName
        ) &&
          modalType === "delete" && (
            <AnimatePresence>
              {openModal && (
                <Modal
                  title={`ნამდვილად გსურთ ${modalTitle}-ის წაშლა?`}
                  handleModalClose={handleModalClose}
                >
                  <div
                    className="mt-3 d-flex align-items-center justify-content-center"
                    style={{ gap: 8 }}
                  >
                    <button
                      className="btn btn-primary"
                      onClick={() => setOpenModal(false)}
                    >
                      გამოსვლა
                    </button>
                    {isSubmitting ? (
                      <SubmitLoader type="danger" margin="mt-0" />
                    ) : (
                      <button
                        className="btn btn-danger"
                        onClick={() => handleDataDelete(id)}
                      >
                        წაშლა
                      </button>
                    )}
                  </div>
                </Modal>
              )}
            </AnimatePresence>
          )}

        {pageInfo.routeName !== "academic" &&
        pageInfo.routeName !== "invited" &&
        pageInfo.routeName !== "administration" &&
        pageInfo.routeName !== "surveys" &&
        pageInfo.routeName !== "bachelor" &&
        pageInfo.routeName !== "master" &&
        pageInfo.routeName !== "tcc" &&
        pageInfo.routeName !== "hse" &&
        pageInfo.routeName !== "phd" &&
        modalType !== "students-view" &&
        modalType !== "students-subjects" &&
        modalType !== "past-subjects" ? (
          <AnimatePresence>
            {openModal && (
              <Modal
                title={
                  showEditForm
                    ? `${langs[locale]["edit"]}: ${modalTitle}`
                    : showPassRecoveryForm
                    ? `${langs[locale]["passowrd_recovery"]}: ${modalTitle}`
                    : showEdoc
                    ? langs[locale]["e_doc_create"]
                    : showSurveys
                    ? langs[locale]["surveys"]
                    : showPaymentsForm
                    ? langs[locale]["finances"]
                    : showUsersList
                    ? langs[locale]["users"]
                    : `ნამდვილად გსურთ ${modalTitle}-ის წაშლა?`
                }
                showPaymentsForm={showPaymentsForm}
                handleModalClose={handleModalClose}
              >
                {showEditForm ? (
                  <>
                    {!(
                      pageInfo.routeName === "students" ||
                      pageInfo.routeName === "lecturers" ||
                      pageInfo.routeName === "library-lmb" ||
                      pageInfo.routeName === "news" ||
                      pageInfo.routeName === "roles" ||
                      pageInfo.routeName === "auditoriums" ||
                      pageInfo.routeName === "edoc" ||
                      pageInfo.routeName === "administrations" ||
                      pageInfo.routeName === "finance-statement"
                    ) && (
                      <EditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}

                    {pageInfo.routeName === "lecturers" && (
                      <LecturerEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                        mode="edit"
                      />
                    )}

                    {pageInfo.routeName === "students" && (
                      <StudentEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}

                    {pageInfo.routeName === "library-lmb" && (
                      <LibraryEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                    {pageInfo.routeName === "finance-statement" && (
                      <StatementEditForm
                        data={data}
                        fields={fields}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                    {pageInfo.routeName === "auditoriums" && (
                      <AuditoriumEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                    {pageInfo.routeName === "roles" && (
                      <RoleEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                    {pageInfo.routeName === "administrations" && (
                      <AdminEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                    {pageInfo.routeName === "news" && (
                      <NewsEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                    {pageInfo.routeName === "edoc" && (
                      <EdocEditForm
                        data={data}
                        fields={fields}
                        fetchLink={fetchLink}
                        id={id}
                        handleDataEdit={handleDataEdit}
                        setShowEditForm={setShowEditForm}
                        setOpenModal={setOpenModal}
                      />
                    )}
                  </>
                ) : showPassRecoveryForm ? (
                  <PassRecoveryForm
                    handleModalClose={handleModalClose}
                    id={data.user_id}
                  />
                ) : showSurveys ? (
                  <SurveysList
                    syllabusId={data.id}
                    lecturers={data.lecturers}
                  />
                ) : showEdoc && modalType !== "delete" ? (
                  <EdocTable userId={data.user_id} pageInfo={pageInfo} />
                ) : showUsersList ? (
                  <UsersList />
                ) : showPaymentsForm ? (
                  <FinanceReport
                    id={data.id}
                    name={data.name + " " + data.surname}
                  />
                ) : (
                  <div
                    className="mt-3 d-flex align-items-center justify-content-center"
                    style={{ gap: 8 }}
                  >
                    <button
                      className="btn btn-primary"
                      onClick={() => setOpenModal(false)}
                    >
                      გამოსვლა
                    </button>
                    {isSubmitting ? (
                      <SubmitLoader type="danger" margin="mt-0" />
                    ) : (
                      <button
                        className="btn btn-danger"
                        onClick={() => handleDataDelete(id)}
                      >
                        წაშლა
                      </button>
                    )}
                  </div>
                )}
              </Modal>
            )}
          </AnimatePresence>
        ) : dataId === data.id ? (
          <FullModal
            setOpenModal={setOpenModal}
            openModal={openModal}
            modalType={modalType}
            id={data.id}
            setModalType={setModalType}
          >
            {pageInfo.routeName === "academic" && modalType === "edit" && (
              <AcademicForm data={data} setOpenModal={setOpenModal} />
            )}

            {pageInfo.routeName === "administration" &&
              modalType === "edit" && (
                <AdminForm data={data} setOpenModal={setOpenModal} />
              )}

            {pageInfo.routeName === "invited" && modalType === "edit" && (
              <InvitedForm data={data} setOpenModal={setOpenModal} />
            )}
            {(pageInfo.routeName === "administration" ||
              pageInfo.routeName === "academic" ||
              pageInfo.routeName === "invited") &&
              modalType === "show" && (
                <UserView
                  data={data}
                  routeName={pageInfo.routeName}
                  setOpenModal={setOpenModal}
                />
              )}
            {pageInfo.routeName === "surveys" && modalType === "surveys" && (
              <SurveyEditForm
                data={data}
                setOpenModal={setOpenModal}
                setModalType={setModalType}
              />
            )}
            {pageInfo.routeName === "surveys" &&
              modalType === "surveys-view" && (
                <SurveyView
                  data={data}
                  setOpenModal={setOpenModal}
                  setModalType={setModalType}
                />
              )}
            {pageInfo.routeName === "bachelor" &&
              modalType === "bachelor-view" && (
                <BachelorCard data={data} routeName={pageInfo.routeName} />
              )}
            {pageInfo.routeName === "master" && modalType === "master-view" && (
              <MasterCard data={data} routeName={pageInfo.routeName} />
            )}
            {pageInfo.routeName === "phd" && modalType === "phd-view" && (
              <DoctorCard data={data} routeName={pageInfo.routeName} />
            )}
            {pageInfo.routeName === "tcc" && modalType === "tcc-view" && (
              <TrainingCard data={data} routeName={pageInfo.routeName} />
            )}
            {pageInfo.routeName === "hse" && modalType === "hse-view" && (
              <ProfessionCard data={data} routeName={pageInfo.routeName} />
            )}
          </FullModal>
        ) : null}

        {pageInfo.routeName === "students" && modalType === "students-view" ? (
          <FullModal
            setOpenModal={setOpenModal}
            openModal={openModal}
            modalType={modalType}
            setModalType={setModalType}
          >
            <StudentCard data={data} routeName={pageInfo.routeName} />
          </FullModal>
        ) : modalType === "students-subjects" && dataId === data.id ? (
          <FullModal
            setOpenModal={setOpenModal}
            openModal={openModal}
            modalType={modalType}
            setModalType={setModalType}
          >
            <CurrentSubjects
              id={dataId}
              name={data.name + " " + data.surname}
            />
          </FullModal>
        ) : modalType === "past-subjects" && dataId === data.id ? (
          <FullModal
            setOpenModal={setOpenModal}
            openModal={openModal}
            modalType={modalType}
            setModalType={setModalType}
          >
            <PassedSubjects id={dataId} name={data.name + " " + data.surname} />
          </FullModal>
        ) : null}
        {userDeleted && (
          <SweetAlert2 {...swalProps} onConfirm={() => setOpenModal(false)} />
        )}

        {/* Diploma Language Selection Modal */}
        <AnimatePresence>
          {showDiplomaLangModal && (
            <Modal
              title={locale && langs[locale]["download_diploma"] || "დიპლომის დანართი"}
              handleModalClose={() => setShowDiplomaLangModal(false)}
            >
              <div className="d-flex flex-column align-items-center justify-content-center p-4">
                <p className="mb-4">{locale && langs[locale]["select_language"] || "აირჩიეთ ენა"}</p>
                <div className="d-flex gap-3">
                  <button
                    className="btn btn-primary px-4 py-3"
                    onClick={() => downloadDiplomaWithLang('geo')}
                  >
                    <img
                      src="/assets/media/georgia-circle.png"
                      alt="Georgian flag"
                      style={{ width: '24px', height: '24px', marginRight: '8px' }}
                    />
                    {locale && langs[locale]["georgian"] || "ქართული"}
                  </button>
                  <button
                    className="btn btn-primary px-4 py-3"
                    onClick={() => downloadDiplomaWithLang('eng')}
                  >
                    <img
                      src="/assets/media/united-states-circle.png"
                      alt="English flag"
                      style={{ width: '24px', height: '24px', marginRight: '8px' }}
                    />
                    {locale && langs[locale]["english"] || "ინგლისური"}
                  </button>
                </div>
              </div>
            </Modal>
          )}
        </AnimatePresence>
        </td>
      )}
    </tr>
  );
}

export default TableRow;
