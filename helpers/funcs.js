import { MONTHS } from "./../components/projectData";

export const makeArray = (data) => {
  return Object.entries(data).map((item) => {
    return { id: item[0], name: item[1] };
  });
};

export const dateFormat = (date, type, separator, sort) => {
  const dateValue = date ? new Date(date) : new Date();
  let hh, min;
  let dd = dateValue.getDate();
  let mm = dateValue.getMonth() + 1;
  mm = mm < 10 ? "0" + mm : mm;
  dd = dd < 10 ? "0" + dd : dd;
  const yy = dateValue.getFullYear();
  if (type) {
    hh = dateValue.getHours();
    hh = hh < 10 ? "0" + hh : hh;
    min = dateValue.getMinutes();
    min = min < 10 ? "0" + min : min;
    const dateString =
      sort === "desc"
        ? yy + separator + mm + separator + dd + " " + hh + ":" + min
        : dd + separator + mm + separator + yy + " " + hh + ":" + min;
    return dateString;
  } else {
    const dateString =
      sort === "desc"
        ? yy + separator + mm + separator + dd
        : dd + separator + mm + separator + yy;
    return dateString;
  }
};

export const timeFormat = (date) => {
  const dateValue = new Date(date);
  let hh, min;

  hh = dateValue.getHours();
  hh = hh < 10 ? "0" + hh : hh;
  min = dateValue.getMinutes();
  min = min < 10 ? "0" + min : min;
  const dateString = hh + ":" + min;
  console.log(dateString);
  return dateString;
};

export const capitalize = (str) => {
  if (str) {
    return str[0].toUpperCase() + str.slice(1);
  }
};

// export const hasNull = (data) => {
//   for(let key in data) {
//     if(data[key] === null) {
//       data[key] = ''
//     }
//   }

//   return data
// }

export const hasNull = (object) => {
  for (let key in object) {
    if (typeof object[key] === "object" && object[key] !== null) {
      hasNull(object[key]);
    } else {
      if (object[key] === null) {
        object[key] = "";
      }
    }
  }
  return object;
};

export const makeTree = (data) => {
  const idMapping = data.reduce((acc, el, i) => {
    acc[el.id] = i;
    return acc;
  }, {});

  let root;
  data.forEach((el) => {
    // Handle the root element
    if (el.parentId === null) {
      root = el;
      return;
    }
    // Use our mapping to locate the parent element in our data array
    const parentEl = data[idMapping[el.parentId]];
    // Add our current el to its parent's `children` array
    parentEl.children = [...(parentEl.children || []), el];
  });

  return root;
};

export const getBirthDate = (str) => {
  const arr = str.split("-");
  const string =
    arr[0] +
    " " +
    MONTHS.find((item) => item.id === arr[1]).name +
    ", " +
    arr[arr.length - 1];
  return string;
};

export const heilString = () => {
  const date = new Date();
  const hh = date.getHours();
  const mm = date.getMinutes();
  const ss = date.getSeconds();

  if (hh >= "00" && mm > "00" && hh < "06") {
    return "night";
  } else if (hh >= "06" && mm > "00" && hh < "12") {
    return "morning";
  } else if (hh >= "12" && mm > "00" && hh < "18") {
    return "afternoon";
  } else if (hh >= "18" && mm > "00" && hh < "23") {
    return "evening";
  }
};

export const filterFinancesByQuarter = (arr) => {
  const newArray = [];
  const uniqueIds = [];

  for (let i = 0; i < arr.length; i++) {
    if (!uniqueIds.includes(arr[i].personalId)) {
      uniqueIds.push(arr[i].personalId);
    }
  }

  console.log(uniqueIds);

  for (let i = 0; i < uniqueIds.length; i++) {
    let num = 0;
    const chunk = [];
    for (let j = 0; j < arr.length; j++) {
      if (arr[j].personalId === uniqueIds[i]) {
        chunk.push(arr[j]);
      }
    }

    for (let k = 0; k < chunk.length; k++) {
      if (num < chunk[k].quarter) {
        num = chunk[k].quarter;
      }
    }

    newArray.push(chunk.filter((item) => item.quarter === num)[0]);
  }

  console.log(newArray, uniqueIds);
  return newArray;
};

export const getHumanReadDate = (str) => {
  const now = new Date().getTime();
  const date = new Date(str).getTime();
  const interval = now - date;
  const y = Math.floor(interval / (1000 * 60 * 60 * 24 * 365));
  const month = Math.floor(interval / (1000 * 60 * 60 * 24 * 30));
  const d = Math.floor(interval / (1000 * 60 * 60 * 24));
  const h = Math.floor(interval / (1000 * 60 * 60));
  const min = Math.floor(interval / (1000 * 60));
  // console.log(m, h, d, date)
  if (y) {
    return `${y} years ago`;
  } else if (month) {
    return `${month} months ago`;
  } else if (d) {
    return `${d} days ago`;
  } else if (h) {
    return `${h} hours ago`;
  } else if (min) {
    return `${min} minutes ago`;
  } else {
    return "now";
  }
};

export const calculateMonthsBetweenDates = (startDate, endDate) => {
  if (!startDate || !endDate) {
    return 0;
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  // Calculate the difference in months
  const yearDiff = end.getFullYear() - start.getFullYear();
  const monthDiff = end.getMonth() - start.getMonth();

  return yearDiff * 12 + monthDiff;
};

export const preventNonDigit = (e) => {
  if (
    (e.which < 48 || (e.which > 57 && e.which < 96) || e.which > 105) &&
    e.which !== 8 &&
    e.which !== 9
  ) {
    e.preventDefault();
  }
};

export const getFinanceStatements = (data) => {
  return data.map((item) => {
    let newObject = "";
    newObject = {
      ...item,
      student: item.student.name + " " + item.student.surname,
      personal_id: item.student.personal_id,
      program: item.student.program.name_ka,
    };
    return newObject;
  });
};
